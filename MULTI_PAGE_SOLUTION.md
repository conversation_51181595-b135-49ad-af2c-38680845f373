# 多页面支持解决方案

## 问题分析

您指出的问题确实存在且很重要：

### 原有问题
1. **只支持单页面**：`ReliableJsonReplayExecutor` 和 `ScriptReplayExecutor` 都只有一个页面实例
2. **录制数据缺少页面标识符**：Playwright 录制的 JSON 数据中没有 `pageAlias` 字段
3. **无法处理多 tab 场景**：当用户在多个标签页间切换操作时，执行器无法确定应该在哪个页面执行动作

### 官方 Playwright 的处理方式
- 使用 `context.pages()` 获取所有页面
- 使用 `context.waitForEvent('page')` 监听新页面创建
- 使用页面别名或索引来区分不同的页面
- 在录制数据中包含页面信息

## 解决方案

### 1. 多页面管理架构

#### ReliableJsonReplayExecutor 增强
```javascript
class ReliableJsonReplayExecutor {
  constructor() {
    this.browser = null;
    this.context = null;
    this.pages = new Map(); // pageAlias -> Page
    this.pageAliases = new Map(); // Page -> pageAlias
    this.currentPageAlias = 'page'; // 当前活动页面别名
    this.pageCounter = 0; // 页面计数器，用于生成唯一别名
  }
}
```

#### 主要功能
- **页面注册与管理**：`_registerPage()`, `_unregisterPage()`
- **页面监听**：`_setupPageListeners()` 自动监听新页面创建和关闭
- **页面切换**：`switchToPage()`, `getPageByAlias()`
- **页面信息获取**：`getAllPagesInfo()`, `listPages()`

### 2. 动作执行流程

#### 页面解析逻辑
```javascript
_extractPageAlias(actionData) {
  // 优先使用动作数据中的页面别名
  if (actionData.pageAlias) {
    return actionData.pageAlias;
  }
  
  // 检查是否有页面索引
  if (actionData.pageIndex !== undefined) {
    const pageAliases = Array.from(this.pages.keys());
    return pageAliases[actionData.pageIndex] || this.currentPageAlias;
  }
  
  // 默认使用当前页面
  return this.currentPageAlias;
}
```

#### 动作执行步骤
1. **解析目标页面**：从动作数据中提取页面别名
2. **验证页面有效性**：检查页面是否存在且未关闭
3. **页面切换**：如果需要，切换到目标页面
4. **执行动作**：在指定页面上执行动作
5. **错误处理**：提供详细的页面诊断信息

### 3. 脚本执行器继承

```javascript
class ScriptReplayExecutor extends ReliableJsonReplayExecutor {
  constructor() {
    super(); // 继承多页面管理功能
    this.monitoringActive = false;
    this.checkpointResults = [];
    this.outputResults = [];
  }
}
```

#### 检查点和变量提取的多页面支持
- 检查点可以指定 `pageAlias` 在特定页面验证
- 输出变量可以从不同页面提取数据
- 监听模式支持跨页面检查点验证

### 4. 特殊动作处理

#### 页面创建
```javascript
async _handleOpenPage(actionData) {
  const newPage = await this.context.newPage();
  const pageAlias = this._extractPageAlias(actionData);
  await this._registerPage(newPage, pageAlias);
  
  if (actionData.url) {
    await newPage.goto(actionData.url);
  }
}
```

#### 页面关闭
```javascript
async _handleClosePage(actionData) {
  const pageAlias = this._extractPageAlias(actionData);
  const page = this.getPageByAlias(pageAlias);
  
  if (page && !page.isClosed()) {
    await page.close();
  }
}
```

## 使用方法

### 1. 动作数据中指定页面
```javascript
// 在特定页面执行动作
await executor.executeAction({
  name: 'click',
  selector: '#button',
  pageAlias: 'page1'  // 指定页面别名
});

// 使用页面索引
await executor.executeAction({
  name: 'fill',
  selector: '#input',
  text: 'Hello',
  pageIndex: 0  // 使用页面索引
});
```

### 2. 页面管理
```javascript
// 打开新页面
await executor.executeAction({
  name: 'openPage',
  url: 'https://example.com'
});

// 切换页面
executor.switchToPage('page1');

// 获取页面信息
const pagesInfo = executor.getAllPagesInfo();
console.log('所有页面:', pagesInfo);

// 列出页面
executor.listPages();
```

### 3. 脚本中的多页面支持
```javascript
// 检查点指定页面
{
  name: "登录检查",
  type: "visibility",
  selector: "#user-menu",
  pageAlias: "main"
}

// 输出变量指定页面
{
  name: "用户名",
  type: "text",
  selector: "#username",
  pageAlias: "profile"
}
```

## 兼容性处理

### 1. 向后兼容
- 如果动作数据中没有页面信息，使用当前页面
- 保持原有的单页面使用方式不变

### 2. 错误处理
- 页面不存在时，自动回退到当前页面
- 提供详细的页面诊断信息
- 页面关闭时自动切换到其他可用页面

### 3. 性能优化
- 页面监听器自动管理生命周期
- 内存泄漏防护
- 智能页面切换

## 测试用例

运行多页面测试：
```bash
node test-multi-page.js
```

测试内容：
1. 页面创建和管理
2. 页面间切换
3. 在不同页面执行操作
4. 页面信息查看
5. 页面关闭处理

## 总结

这个解决方案完全解决了您指出的多页面问题：

1. **✅ 支持多页面管理**：可以同时管理多个标签页
2. **✅ 页面别名系统**：通过别名精确控制页面操作
3. **✅ 自动页面监听**：自动处理页面创建和关闭
4. **✅ 向后兼容**：不影响现有的单页面使用方式
5. **✅ 详细诊断信息**：提供丰富的页面状态信息

现在您的脚本可以正确处理多 tab 场景，每个动作都会在正确的页面上执行。 