/**
 * 检查 patch 是否正确应用
 * 不需要运行 Playwright，只检查文件内容
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 检查 Playwright 回放 API patch 应用状态');
console.log('='.repeat(50));

// 检查文件是否存在
const recorderRunnerPath = path.join(__dirname, 'node_modules/playwright-core/lib/server/recorder/recorderRunner.js');
const recorderUtilsPath = path.join(__dirname, 'node_modules/playwright-core/lib/server/recorder/recorderUtils.js');

console.log('\n📁 检查文件存在性:');
console.log('recorderRunner.js:', fs.existsSync(recorderRunnerPath) ? '✅ 存在' : '❌ 不存在');
console.log('recorderUtils.js:', fs.existsSync(recorderUtilsPath) ? '✅ 存在' : '❌ 不存在');

if (!fs.existsSync(recorderRunnerPath) || !fs.existsSync(recorderUtilsPath)) {
  console.log('❌ 必要文件不存在，无法继续检查');
  process.exit(1);
}

// 检查 recorderRunner.js 的 patch
console.log('\n📋 检查 recorderRunner.js patch:');
const runnerContent = fs.readFileSync(recorderRunnerPath, 'utf-8');

const runnerChecks = [
  { name: 'GLOBAL_REPLAY_API_PATCH 注释', pattern: /GLOBAL_REPLAY_API_PATCH/ },
  { name: 'global.playwrightReplayAPI 对象', pattern: /global\.playwrightReplayAPI\s*=/ },
  { name: 'performAction 暴露', pattern: /performAction:\s*performAction/ },
  { name: 'toClickOptions 暴露', pattern: /toClickOptions:\s*toClickOptions/ },
  { name: 'buildFullSelector 导入', pattern: /buildFullSelector.*require.*recorderUtils/ },
  { name: 'isPatchedAPI 标记', pattern: /isPatchedAPI:\s*true/ },
  { name: '控制台日志', pattern: /console\.log.*Playwright 回放 API 已暴露/ }
];

let runnerPassed = 0;
for (const check of runnerChecks) {
  const passed = check.pattern.test(runnerContent);
  console.log(`  ${check.name}: ${passed ? '✅' : '❌'}`);
  if (passed) runnerPassed++;
}

console.log(`recorderRunner.js patch 状态: ${runnerPassed}/${runnerChecks.length} 通过`);

// 检查 recorderUtils.js 的 patch
console.log('\n📋 检查 recorderUtils.js patch:');
const utilsContent = fs.readFileSync(recorderUtilsPath, 'utf-8');

const utilsChecks = [
  { name: 'GLOBAL_UTILS_API_PATCH 注释', pattern: /GLOBAL_UTILS_API_PATCH/ },
  { name: 'Object.assign 扩展', pattern: /Object\.assign.*global\.playwrightReplayAPI/ },
  { name: 'createActionInContext 函数', pattern: /createActionInContext:\s*function/ },
  { name: 'executeActionSequence 函数', pattern: /executeActionSequence:\s*async\s*function/ },
  { name: 'collapseActions 暴露', pattern: /collapseActions:\s*collapseActions/ },
  { name: '控制台日志', pattern: /console\.log.*工具函数已添加/ }
];

let utilsPassed = 0;
for (const check of utilsChecks) {
  const passed = check.pattern.test(utilsContent);
  console.log(`  ${check.name}: ${passed ? '✅' : '❌'}`);
  if (passed) utilsPassed++;
}

console.log(`recorderUtils.js patch 状态: ${utilsPassed}/${utilsChecks.length} 通过`);

// 总结
console.log('\n📊 总体状态:');
const totalChecks = runnerChecks.length + utilsChecks.length;
const totalPassed = runnerPassed + utilsPassed;

console.log(`总检查项: ${totalChecks}`);
console.log(`通过检查: ${totalPassed}`);
console.log(`通过率: ${((totalPassed / totalChecks) * 100).toFixed(1)}%`);

if (totalPassed === totalChecks) {
  console.log('\n🎉 所有 patch 检查通过！');
  console.log('✅ Playwright 回放 API 已成功暴露到全局');
  console.log('📝 可以使用 global.playwrightReplayAPI 访问官方回放功能');
} else {
  console.log('\n⚠️ 部分 patch 检查失败');
  console.log('请检查 patch 是否正确应用');
}

// 显示一些关键代码片段
console.log('\n📄 关键代码片段预览:');

// 从 recorderRunner.js 提取全局 API 定义
const globalAPIMatch = runnerContent.match(/global\.playwrightReplayAPI\s*=\s*{[\s\S]*?};/);
if (globalAPIMatch) {
  console.log('\n🔧 全局 API 定义:');
  console.log(globalAPIMatch[0].substring(0, 200) + '...');
}

// 从 recorderUtils.js 提取辅助函数
const helperMatch = utilsContent.match(/createActionInContext:\s*function[\s\S]*?},/);
if (helperMatch) {
  console.log('\n🛠️ 辅助函数示例:');
  console.log(helperMatch[0].substring(0, 150) + '...');
}

console.log('\n✨ 检查完成');

// 生成使用示例
console.log('\n💡 使用示例:');
console.log(`
// 1. 加载 playwright-core 以触发 patch
require('playwright-core');

// 2. 使用全局 API
const api = global.playwrightReplayAPI;

// 3. 创建动作
const action = api.createActionInContext('page', [], {
  name: 'click',
  selector: 'button',
  button: 'left',
  modifiers: 0,
  clickCount: 1
});

// 4. 执行动作
await api.performAction(pageAliases, action);
`);
