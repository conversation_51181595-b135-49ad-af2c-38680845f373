/**
 * 调试 ActionInContext 对象结构
 */

// 加载模块
require('./node_modules/playwright-core/lib/server/recorder/recorderRunner.js');
require('./node_modules/playwright-core/lib/server/recorder/recorderUtils.js');

const api = global.playwrightReplayAPI;

console.log('🔍 调试 ActionInContext 对象结构');
console.log('='.repeat(50));

// 测试导航动作
console.log('\n📋 测试导航动作结构:');
const navigateAction = api.createActionInContext('page', [], {
  name: 'navigate',
  url: 'https://example.com'
});

console.log('创建的 navigateAction:');
console.log(JSON.stringify(navigateAction, null, 2));

console.log('\n🔍 详细分析:');
console.log('actionInContext.action:', navigateAction.action);
console.log('actionInContext.action.name:', navigateAction.action.name);
console.log('actionInContext.action.url:', navigateAction.action.url);
console.log('actionInContext.action.url 类型:', typeof navigateAction.action.url);

// 测试点击动作
console.log('\n📋 测试点击动作结构:');
const clickAction = api.createActionInContext('page', [], {
  name: 'click',
  selector: 'button#test',
  button: 'left',
  modifiers: 0,
  clickCount: 1
});

console.log('创建的 clickAction:');
console.log(JSON.stringify(clickAction, null, 2));

console.log('\n🔍 详细分析:');
console.log('actionInContext.action:', clickAction.action);
console.log('actionInContext.action.name:', clickAction.action.name);
console.log('actionInContext.action.selector:', clickAction.action.selector);
console.log('actionInContext.action.selector 类型:', typeof clickAction.action.selector);

// 对比官方期望的结构
console.log('\n📚 官方期望的结构:');
console.log('对于 navigate 动作:');
console.log('  actionInContext.action.name: "navigate"');
console.log('  actionInContext.action.url: string');
console.log('  actionInContext.action.signals: array');

console.log('\n对于 click 动作:');
console.log('  actionInContext.action.name: "click"');
console.log('  actionInContext.action.selector: string');
console.log('  actionInContext.action.button: string');
console.log('  actionInContext.action.modifiers: number');
console.log('  actionInContext.action.clickCount: number');
console.log('  actionInContext.action.signals: array');

// 手动创建一个符合官方格式的对象
console.log('\n🛠️ 手动创建官方格式对象:');
const manualNavigateAction = {
  frame: {
    pageAlias: 'page',
    framePath: []
  },
  action: {
    name: 'navigate',
    url: 'https://example.com',
    signals: []
  },
  startTime: Date.now()
};

console.log('手动创建的 navigateAction:');
console.log(JSON.stringify(manualNavigateAction, null, 2));

// 比较两个对象
console.log('\n🔄 对象比较:');
console.log('API 创建的 url:', navigateAction.action.url);
console.log('手动创建的 url:', manualNavigateAction.action.url);
console.log('两者相等:', navigateAction.action.url === manualNavigateAction.action.url);
console.log('两者类型相同:', typeof navigateAction.action.url === typeof manualNavigateAction.action.url);

console.log('\n✨ 调试完成');
