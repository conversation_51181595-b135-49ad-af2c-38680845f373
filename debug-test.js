/**
 * 调试全局 API 加载过程
 */

console.log('🔍 调试全局 API 加载过程');

// 清空全局对象（如果存在）
if (global.playwrightReplayAPI) {
  delete global.playwrightReplayAPI;
  console.log('🧹 清空了现有的全局 API');
}

console.log('\n📦 步骤1: 加载 recorderUtils...');
const recorderUtils = require('./node_modules/playwright-core/lib/server/recorder/recorderUtils.js');
console.log('✅ recorderUtils 加载完成');

if (global.playwrightReplayAPI) {
  console.log('📊 recorderUtils 加载后的全局 API:');
  console.log('  函数数量:', Object.keys(global.playwrightReplayAPI).filter(k => typeof global.playwrightReplayAPI[k] === 'function').length);
  console.log('  函数列表:', Object.keys(global.playwrightReplayAPI).filter(k => typeof global.playwrightReplayAPI[k] === 'function'));
  console.log('  createActionInContext 存在:', typeof global.playwrightReplayAPI.createActionInContext);
} else {
  console.log('❌ recorderUtils 加载后全局 API 不存在');
}

console.log('\n📦 步骤2: 加载 recorderRunner...');
const recorderRunner = require('./node_modules/playwright-core/lib/server/recorder/recorderRunner.js');
console.log('✅ recorderRunner 加载完成');

if (global.playwrightReplayAPI) {
  console.log('📊 recorderRunner 加载后的全局 API:');
  console.log('  函数数量:', Object.keys(global.playwrightReplayAPI).filter(k => typeof global.playwrightReplayAPI[k] === 'function').length);
  console.log('  函数列表:', Object.keys(global.playwrightReplayAPI).filter(k => typeof global.playwrightReplayAPI[k] === 'function'));
  console.log('  createActionInContext 存在:', typeof global.playwrightReplayAPI.createActionInContext);
  console.log('  performAction 存在:', typeof global.playwrightReplayAPI.performAction);
} else {
  console.log('❌ recorderRunner 加载后全局 API 不存在');
}

console.log('\n🧪 测试 createActionInContext...');
if (global.playwrightReplayAPI && typeof global.playwrightReplayAPI.createActionInContext === 'function') {
  try {
    const result = global.playwrightReplayAPI.createActionInContext('page', [], {
      name: 'click',
      selector: 'button'
    });
    console.log('✅ createActionInContext 测试成功');
    console.log('  结果:', JSON.stringify(result, null, 2));
  } catch (error) {
    console.log('❌ createActionInContext 测试失败:', error.message);
  }
} else {
  console.log('❌ createActionInContext 函数不可用');
}

// 手动测试 Object.assign
console.log('\n🔧 手动测试 Object.assign...');
if (global.playwrightReplayAPI) {
  console.log('当前全局 API 对象:', Object.keys(global.playwrightReplayAPI));
  
  // 手动添加函数
  global.playwrightReplayAPI.testFunction = function() {
    return 'test';
  };
  
  console.log('手动添加后:', Object.keys(global.playwrightReplayAPI));
  console.log('testFunction 类型:', typeof global.playwrightReplayAPI.testFunction);
  
  if (typeof global.playwrightReplayAPI.testFunction === 'function') {
    console.log('testFunction 调用结果:', global.playwrightReplayAPI.testFunction());
  }
}

console.log('\n✨ 调试完成');
