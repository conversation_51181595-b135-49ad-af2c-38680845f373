# Playwright 全局回放 API 补丁

通过 patch 方式将 Playwright 官方的回放接口暴露到全局对象，使得可以在任何地方直接使用官方的回放逻辑。

## 🎯 目标

- ✅ 将 Playwright 官方的 `performAction` 函数暴露到全局
- ✅ 提供完整的 iframe 处理逻辑（`buildFullSelector`）
- ✅ 支持所有官方动作类型（click, fill, navigate, press 等）
- ✅ 保持与官方 API 100% 兼容
- ✅ 提供辅助函数简化使用

## 🚀 快速开始

### 1. 应用补丁

```bash
# 安装依赖后自动应用补丁
npm install
npm run postinstall
```

### 2. 使用全局 API

```javascript
// 确保先加载 playwright-core 以触发补丁
require('playwright-core');

// 现在可以使用全局 API
const api = global.playwrightReplayAPI;

console.log('API 信息:', {
  version: api.version,
  isPatchedAPI: api.isPatchedAPI,
  functions: Object.keys(api).filter(key => typeof api[key] === 'function')
});
```

## 📚 API 参考

### 核心函数

#### `performAction(pageAliases, actionInContext)`
执行单个动作，完全兼容官方实现。

```javascript
const { chromium } = require('playwright');
const browser = await chromium.launch();
const page = await browser.newPage();
const pageAliases = new Map();
pageAliases.set(page, 'page');

// 执行点击动作
const clickAction = {
  frame: { pageAlias: 'page', framePath: [] },
  action: {
    name: 'click',
    selector: 'button#submit',
    button: 'left',
    modifiers: 0,
    clickCount: 1,
    signals: []
  },
  startTime: Date.now()
};

await global.playwrightReplayAPI.performAction(pageAliases, clickAction);
```

#### `buildFullSelector(framePath, selector)`
构建完整的选择器，处理 iframe 嵌套。

```javascript
const framePath = ['iframe[src*="example"]', 'iframe#nested'];
const selector = 'button#submit';
const fullSelector = global.playwrightReplayAPI.buildFullSelector(framePath, selector);
// 结果: "iframe[src*=\"example\"] >> internal:control=enter-frame >> iframe#nested >> internal:control=enter-frame >> button#submit"
```

### 辅助函数

#### `createActionInContext(pageAlias, framePath, action)`
创建标准的 ActionInContext 对象。

```javascript
const actionInContext = global.playwrightReplayAPI.createActionInContext('page', [], {
  name: 'fill',
  selector: 'input[name="username"]',
  text: 'testuser'
});
```

#### `executeActionSequence(pageAliases, actions)`
批量执行动作序列。

```javascript
const actions = [
  api.createActionInContext('page', [], { name: 'navigate', url: 'https://example.com' }),
  api.createActionInContext('page', [], { name: 'fill', selector: 'input', text: 'test' }),
  api.createActionInContext('page', [], { name: 'click', selector: 'button', button: 'left', modifiers: 0, clickCount: 1 })
];

const results = await global.playwrightReplayAPI.executeActionSequence(pageAliases, actions);
```

## 🎬 支持的动作类型

| 动作类型 | 描述 | 必需参数 |
|---------|------|----------|
| `navigate` | 页面导航 | `url` |
| `click` | 点击元素 | `selector`, `button`, `modifiers`, `clickCount` |
| `fill` | 填写输入框 | `selector`, `text` |
| `press` | 按键操作 | `selector`, `key`, `modifiers` |
| `check` | 勾选复选框 | `selector` |
| `uncheck` | 取消勾选 | `selector` |
| `select` | 选择下拉选项 | `selector`, `options` |
| `setInputFiles` | 上传文件 | `selector`, `files` |

## 🔧 高级用法

### 处理 iframe

```javascript
// 在嵌套 iframe 中执行动作
const framePath = ['iframe#main', 'iframe.content'];
const actionInContext = api.createActionInContext('page', framePath, {
  name: 'click',
  selector: 'button.submit',
  button: 'left',
  modifiers: 0,
  clickCount: 1
});

await api.performAction(pageAliases, actionInContext);
```

### 错误处理

```javascript
try {
  await api.performAction(pageAliases, actionInContext);
} catch (error) {
  console.error('动作执行失败:', error.message);
  // 处理错误...
}
```

### 自定义执行器

```javascript
class CustomReplayExecutor {
  constructor() {
    this.api = global.playwrightReplayAPI;
    this.pageAliases = new Map();
  }
  
  async executeScript(browser, script) {
    const context = await browser.newContext();
    const page = await context.newPage();
    this.pageAliases.set(page, 'page');
    
    try {
      for (const actionData of script.actions) {
        const actionInContext = this.api.createActionInContext(
          actionData.pageAlias || 'page',
          actionData.framePath || [],
          actionData
        );
        
        await this.api.performAction(this.pageAliases, actionInContext);
        
        // 可选：添加延迟
        if (script.slowMo) {
          await page.waitForTimeout(script.slowMo);
        }
      }
    } finally {
      await context.close();
    }
  }
}
```

## 🧪 测试

运行测试以验证补丁是否正确应用：

```bash
node test-global-replay-api.js
```

测试包括：
- ✅ 全局 API 可用性检查
- ✅ 核心函数功能测试
- ✅ 实际浏览器操作测试
- ✅ 错误处理测试
- ✅ 性能测试
- ✅ 兼容性测试

## 📋 补丁详情

补丁修改了以下文件：

1. **`recorder.js`** - 添加 Electron 集成支持
2. **`recorderRunner.js`** - 暴露核心回放函数到全局
3. **`recorderUtils.js`** - 暴露工具函数和辅助方法

### 补丁内容

```diff
// 在 recorderRunner.js 中添加
+// GLOBAL_REPLAY_API_PATCH - 将回放接口暴露到全局
+if (typeof global !== 'undefined') {
+  global.playwrightReplayAPI = {
+    performAction: performAction,
+    toClickOptions: toClickOptions,
+    buildFullSelector: require('./recorderUtils').buildFullSelector,
+    mainFrameForAction: require('./recorderUtils').mainFrameForAction,
+    frameForAction: require('./recorderUtils').frameForAction,
+    version: require('../../package.json').version || 'unknown',
+    isPatchedAPI: true,
+    patchVersion: '1.0.0'
+  };
+}
```

## ⚠️ 注意事项

1. **版本兼容性**: 补丁基于 Playwright 1.53.1，其他版本可能需要调整
2. **加载顺序**: 必须先 `require('playwright-core')` 才能使用全局 API
3. **错误处理**: 保持与官方 API 相同的错误处理行为
4. **性能**: 全局 API 的性能与直接使用官方 API 相同

## 🔄 更新补丁

当 Playwright 版本更新时，可能需要更新补丁：

1. 检查新版本的 API 变化
2. 更新补丁文件中的版本号
3. 测试所有功能是否正常
4. 重新应用补丁：`npm run postinstall`

## 🤝 贡献

如果发现问题或有改进建议，请：

1. 运行测试确认问题
2. 检查补丁是否正确应用
3. 提供详细的错误信息和复现步骤

## 📄 许可证

与 Playwright 保持相同的许可证。
