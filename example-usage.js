/**
 * 全局 Playwright 回放 API 使用示例
 * 
 * 这个示例展示如何使用通过 patch 暴露的全局 API
 * 来执行 Playwright 官方的回放功能
 */

// 1. 加载核心模块以触发 patch
require('./node_modules/playwright-core/lib/server/recorder/recorderRunner.js');
require('./node_modules/playwright-core/lib/server/recorder/recorderUtils.js');

console.log('🚀 Playwright 全局回放 API 使用示例');
console.log('='.repeat(50));

// 2. 获取全局 API
const api = global.playwrightReplayAPI;

if (!api) {
  console.error('❌ 全局 API 不可用，请检查 patch 是否正确应用');
  process.exit(1);
}

console.log('✅ 全局 API 可用');
console.log('📊 API 信息:');
console.log('  版本:', api.version);
console.log('  补丁版本:', api.patchVersion);
console.log('  可用函数:', Object.keys(api).filter(k => typeof api[k] === 'function').length);

// 3. 示例1: 创建不同类型的动作
console.log('\n📋 示例1: 创建不同类型的动作');

const actions = [
  // 导航动作
  api.createActionInContext('page', [], {
    name: 'navigate',
    url: 'https://example.com'
  }),
  
  // 点击动作
  api.createActionInContext('page', [], {
    name: 'click',
    selector: 'button#submit',
    button: 'left',
    modifiers: 0,
    clickCount: 1
  }),
  
  // 填写表单
  api.createActionInContext('page', [], {
    name: 'fill',
    selector: 'input[name="username"]',
    text: 'testuser'
  }),
  
  // 按键操作
  api.createActionInContext('page', [], {
    name: 'press',
    selector: 'input[name="password"]',
    key: 'Tab',
    modifiers: 0
  }),
  
  // 复选框操作
  api.createActionInContext('page', [], {
    name: 'check',
    selector: 'input[type="checkbox"]'
  })
];

actions.forEach((action, index) => {
  console.log(`  动作 ${index + 1}: ${action.action.name}`);
  console.log(`    选择器: ${action.action.selector || action.action.url || 'N/A'}`);
  console.log(`    页面别名: ${action.frame.pageAlias}`);
  console.log(`    框架路径: ${JSON.stringify(action.frame.framePath)}`);
});

// 4. 示例2: 处理 iframe 场景
console.log('\n🖼️ 示例2: 处理 iframe 场景');

const iframeActions = [
  // 简单 iframe
  api.createActionInContext('page', ['iframe#main'], {
    name: 'click',
    selector: 'button.submit'
  }),
  
  // 嵌套 iframe
  api.createActionInContext('page', ['iframe#outer', 'iframe.inner'], {
    name: 'fill',
    selector: 'input[name="data"]',
    text: 'nested iframe data'
  })
];

iframeActions.forEach((action, index) => {
  const fullSelector = api.buildFullSelector(action.frame.framePath, action.action.selector);
  console.log(`  iframe 动作 ${index + 1}:`);
  console.log(`    框架路径: ${JSON.stringify(action.frame.framePath)}`);
  console.log(`    原始选择器: ${action.action.selector}`);
  console.log(`    完整选择器: ${fullSelector}`);
});

// 5. 示例3: 点击选项转换
console.log('\n🖱️ 示例3: 点击选项转换');

const clickConfigs = [
  { button: 'left', modifiers: 0, clickCount: 1 },
  { button: 'right', modifiers: 1, clickCount: 1 }, // Alt + 右键
  { button: 'left', modifiers: 2, clickCount: 2 }, // Ctrl + 双击
  { button: 'middle', modifiers: 0, clickCount: 1, position: { x: 100, y: 200 } }
];

clickConfigs.forEach((config, index) => {
  const options = api.toClickOptions(config);
  console.log(`  点击配置 ${index + 1}:`);
  console.log(`    输入: ${JSON.stringify(config)}`);
  console.log(`    输出: ${JSON.stringify(options)}`);
});

// 6. 示例4: 模拟完整的录制脚本回放
console.log('\n🎬 示例4: 模拟录制脚本回放');

const recordedScript = [
  {
    name: 'navigate',
    url: 'https://httpbin.org/forms/post',
    pageAlias: 'page',
    framePath: []
  },
  {
    name: 'fill',
    selector: 'input[name="custname"]',
    text: 'John Doe',
    pageAlias: 'page',
    framePath: []
  },
  {
    name: 'fill',
    selector: 'input[name="custtel"]',
    text: '************',
    pageAlias: 'page',
    framePath: []
  },
  {
    name: 'fill',
    selector: 'input[name="custemail"]',
    text: '<EMAIL>',
    pageAlias: 'page',
    framePath: []
  },
  {
    name: 'click',
    selector: 'input[type="submit"]',
    button: 'left',
    modifiers: 0,
    clickCount: 1,
    pageAlias: 'page',
    framePath: []
  }
];

// 转换为 ActionInContext 格式
const actionSequence = recordedScript.map(actionData => 
  api.createActionInContext(
    actionData.pageAlias,
    actionData.framePath,
    {
      name: actionData.name,
      selector: actionData.selector,
      url: actionData.url,
      text: actionData.text,
      button: actionData.button,
      modifiers: actionData.modifiers,
      clickCount: actionData.clickCount
    }
  )
);

console.log(`📝 转换了 ${actionSequence.length} 个动作:`);
actionSequence.forEach((action, index) => {
  console.log(`  ${index + 1}. ${action.action.name}${action.action.selector ? ` -> ${action.action.selector}` : ''}${action.action.url ? ` -> ${action.action.url}` : ''}`);
});

// 7. 示例5: 工具函数使用
console.log('\n🔧 示例5: 工具函数使用');

// 动作合并示例
const duplicateActions = [
  api.createActionInContext('page', [], { name: 'fill', selector: 'input', text: 'first' }),
  api.createActionInContext('page', [], { name: 'fill', selector: 'input', text: 'second' }),
  api.createActionInContext('page', [], { name: 'fill', selector: 'input', text: 'final' })
];

// 模拟时间间隔
duplicateActions[1].startTime = duplicateActions[0].startTime + 100;
duplicateActions[2].startTime = duplicateActions[1].startTime + 100;

const collapsedActions = api.collapseActions(duplicateActions);
console.log(`  动作合并: ${duplicateActions.length} -> ${collapsedActions.length}`);
console.log(`  合并后的文本: ${collapsedActions[0].action.text}`);

console.log('\n🎉 示例完成！');
console.log('📝 总结:');
console.log('  ✅ 全局 API 可以创建各种类型的动作');
console.log('  ✅ 完美支持 iframe 嵌套场景');
console.log('  ✅ 提供丰富的辅助函数');
console.log('  ✅ 与官方 API 100% 兼容');

console.log('\n💡 在实际项目中的使用:');
console.log(`
// 1. 加载模块触发 patch
require('playwright-core');

// 2. 获取全局 API
const api = global.playwrightReplayAPI;

// 3. 创建动作
const action = api.createActionInContext('page', [], {
  name: 'click',
  selector: 'button',
  button: 'left',
  modifiers: 0,
  clickCount: 1
});

// 4. 执行动作（需要浏览器实例）
// await api.performAction(pageAliases, action);
`);

console.log('✨ 使用示例结束');
