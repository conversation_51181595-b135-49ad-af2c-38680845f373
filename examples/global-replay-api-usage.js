/**
 * 使用全局暴露的 Playwright 回放 API 示例
 * 
 * 通过 patch 方式，Playwright 的核心回放功能已经暴露到 global.playwrightReplayAPI
 * 这样可以在任何地方直接使用官方的回放逻辑，无需复杂的模块导入
 */

const { chromium } = require('playwright');

async function demonstrateGlobalReplayAPI() {
  console.log('🚀 演示全局 Playwright 回放 API 的使用');
  
  // 检查全局 API 是否可用
  if (!global.playwrightReplayAPI) {
    console.error('❌ 全局 Playwright 回放 API 未找到！');
    console.log('请确保：');
    console.log('1. 已应用 patch: npm run postinstall');
    console.log('2. 已加载 playwright-core 模块');
    return;
  }
  
  console.log('✅ 全局 API 可用:', {
    version: global.playwrightReplayAPI.version,
    isPatchedAPI: global.playwrightReplayAPI.isPatchedAPI,
    patchVersion: global.playwrightReplayAPI.patchVersion,
    availableFunctions: Object.keys(global.playwrightReplayAPI)
  });
  
  // 启动浏览器
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  // 创建页面别名映射（官方 API 需要）
  const pageAliases = new Map();
  pageAliases.set(page, 'page');
  
  try {
    // 示例1: 使用全局 API 执行导航动作
    console.log('\n📍 示例1: 执行导航动作');
    const navigateAction = {
      frame: {
        pageAlias: 'page',
        framePath: []
      },
      action: {
        name: 'navigate',
        url: 'https://example.com',
        signals: []
      },
      startTime: Date.now()
    };
    
    await global.playwrightReplayAPI.performAction(pageAliases, navigateAction);
    console.log('✅ 导航完成');
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    // 示例2: 使用全局 API 的辅助函数
    console.log('\n🔧 示例2: 使用辅助函数');
    
    // 构建完整选择器（处理 iframe）
    const framePath = ['iframe[src*="example"]', 'iframe#nested'];
    const selector = 'button#submit';
    const fullSelector = global.playwrightReplayAPI.buildFullSelector(framePath, selector);
    console.log('🔍 完整选择器:', fullSelector);
    
    // 示例3: 执行点击动作
    console.log('\n🖱️ 示例3: 执行点击动作');
    const clickAction = {
      frame: {
        pageAlias: 'page',
        framePath: []
      },
      action: {
        name: 'click',
        selector: 'h1',
        button: 'left',
        modifiers: 0,
        clickCount: 1,
        signals: []
      },
      startTime: Date.now()
    };
    
    await global.playwrightReplayAPI.performAction(pageAliases, clickAction);
    console.log('✅ 点击完成');
    
    // 示例4: 批量执行动作序列
    console.log('\n📋 示例4: 批量执行动作序列');
    const actionSequence = [
      {
        frame: { pageAlias: 'page', framePath: [] },
        action: { name: 'navigate', url: 'https://httpbin.org/forms/post', signals: [] },
        startTime: Date.now()
      },
      {
        frame: { pageAlias: 'page', framePath: [] },
        action: { 
          name: 'fill', 
          selector: 'input[name="custname"]', 
          text: 'Test User',
          signals: [] 
        },
        startTime: Date.now()
      },
      {
        frame: { pageAlias: 'page', framePath: [] },
        action: { 
          name: 'fill', 
          selector: 'input[name="custtel"]', 
          text: '************',
          signals: [] 
        },
        startTime: Date.now()
      }
    ];
    
    for (const [index, actionInContext] of actionSequence.entries()) {
      console.log(`执行动作 ${index + 1}/${actionSequence.length}: ${actionInContext.action.name}`);
      await global.playwrightReplayAPI.performAction(pageAliases, actionInContext);
      await page.waitForTimeout(500); // 短暂延迟
    }
    
    console.log('✅ 动作序列执行完成');
    
    // 示例5: 错误处理
    console.log('\n⚠️ 示例5: 错误处理');
    try {
      const invalidAction = {
        frame: { pageAlias: 'page', framePath: [] },
        action: { 
          name: 'click', 
          selector: 'non-existent-element',
          button: 'left',
          modifiers: 0,
          clickCount: 1,
          signals: [] 
        },
        startTime: Date.now()
      };
      
      await global.playwrightReplayAPI.performAction(pageAliases, invalidAction);
    } catch (error) {
      console.log('✅ 正确捕获错误:', error.message);
    }
    
  } finally {
    await browser.close();
  }
  
  console.log('\n🎉 全局 API 演示完成！');
}

// 自定义回放执行器，使用全局 API
class GlobalAPIReplayExecutor {
  constructor() {
    this.pageAliases = new Map();
  }
  
  async executeActionSequence(browser, actions) {
    const context = await browser.newContext();
    const page = await context.newPage();
    this.pageAliases.set(page, 'page');
    
    try {
      for (const actionData of actions) {
        const actionInContext = {
          frame: {
            pageAlias: actionData.pageAlias || 'page',
            framePath: actionData.framePath || []
          },
          action: {
            name: actionData.name,
            signals: actionData.signals || [],
            ...this._extractActionData(actionData)
          },
          startTime: Date.now()
        };
        
        console.log(`🎬 执行: ${actionData.name}${actionData.selector ? ` -> ${actionData.selector}` : ''}`);
        await global.playwrightReplayAPI.performAction(this.pageAliases, actionInContext);
      }
    } finally {
      await context.close();
    }
  }
  
  _extractActionData(actionData) {
    const result = {};
    
    // 根据动作类型提取特定数据
    switch (actionData.name) {
      case 'navigate':
        result.url = actionData.url;
        break;
      case 'click':
        result.selector = actionData.selector;
        result.button = actionData.button || 'left';
        result.modifiers = actionData.modifiers || 0;
        result.clickCount = actionData.clickCount || 1;
        if (actionData.position) result.position = actionData.position;
        break;
      case 'fill':
        result.selector = actionData.selector;
        result.text = actionData.text;
        break;
      case 'press':
        result.selector = actionData.selector;
        result.key = actionData.key;
        result.modifiers = actionData.modifiers || 0;
        break;
      default:
        if (actionData.selector) result.selector = actionData.selector;
        break;
    }
    
    return result;
  }
}

// 如果直接运行此文件
if (require.main === module) {
  // 确保先加载 playwright-core 以触发 patch
  require('playwright-core');
  
  demonstrateGlobalReplayAPI().catch(console.error);
}

module.exports = {
  demonstrateGlobalReplayAPI,
  GlobalAPIReplayExecutor
};
