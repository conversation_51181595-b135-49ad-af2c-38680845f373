# Playwright Recorder SDK 测试指南

## 🧪 测试策略概述

本文档详细说明了如何对Playwright Recorder SDK进行全面测试，确保功能的正确性和稳定性。

## 📋 测试分类

### 1. 单元测试 (Unit Tests)
- **目标**: 测试各个管理器类和工具函数的独立功能
- **覆盖范围**: 90%+ 代码覆盖率
- **位置**: `tests/unit/`

### 2. 集成测试 (Integration Tests)  
- **目标**: 测试SDK各模块之间的协作
- **覆盖范围**: 核心工作流程
- **位置**: `tests/integration/`

### 3. 端到端测试 (E2E Tests)
- **目标**: 测试完整的用户使用场景
- **覆盖范围**: 真实浏览器环境下的录制和重放
- **位置**: `tests/e2e/`

### 4. 兼容性测试 (Compatibility Tests)
- **目标**: 测试不同浏览器和Playwright版本的兼容性
- **位置**: `tests/compatibility/`

## 🚀 快速开始

### 安装测试依赖

```bash
cd playwright-recorder-sdk
npm install --dev
```

### 运行所有测试

```bash
npm test
```

### 运行特定测试类型

```bash
# 单元测试
npm run test:unit

# 集成测试  
npm run test:integration

# 端到端测试
npm run test:e2e

# 监视模式
npm run test:watch

# 覆盖率报告
npm run test:coverage
```

## 📊 测试覆盖范围

### 核心功能测试清单

#### ✅ SDK生命周期
- [x] 实例创建
- [x] 初始化流程
- [x] 资源清理
- [ ] 错误恢复机制

#### ✅ 录制功能
- [x] 开始/停止录制
- [x] 暂停/恢复录制
- [x] 模式切换
- [ ] 录制数据完整性验证

#### ✅ 脚本生成
- [x] 多种格式生成 (JS/TS/Python/Java/C#)
- [x] 动作数据结构验证
- [ ] 脚本语法正确性验证

#### ⚠️ 元素选择器 (受补丁限制)
- [x] 启用/禁用选择器模式
- [ ] 元素信息提取 (依赖补丁)
- [ ] 选择器生成算法

#### ⚠️ 脚本重放 (受补丁限制)  
- [x] 基本动作重放API
- [ ] 复杂交互重放验证
- [ ] 重放错误处理

#### ✅ 浏览器管理
- [x] 多浏览器支持
- [x] 页面导航
- [x] 资源清理

## 🛠️ 测试环境配置

### 必需依赖

```json
{
  "devDependencies": {
    "jest": "^29.0.0",
    "playwright": "^1.40.0",
    "express": "^4.18.0",
    "supertest": "^6.3.0"
  }
}
```

### 环境变量

```bash
# 测试模式 (跳过某些需要用户交互的测试)
export TEST_MODE=true

# 启用测试服务器
export START_TEST_SERVER=true

# 测试浏览器类型
export TEST_BROWSER=chromium

# 显示浏览器窗口 (调试用)
export TEST_HEADLESS=false
```

## 📝 编写新测试

### 单元测试示例

```javascript
describe('PlaywrightManager', () => {
  let manager
  
  beforeEach(() => {
    manager = new PlaywrightManager(mockSDK)
  })
  
  afterEach(async () => {
    await manager.cleanup()
  })
  
  test('应该能够启动浏览器', async () => {
    await manager._launchBrowser({ type: 'chromium' })
    expect(manager.browser).toBeTruthy()
  })
})
```

### 集成测试示例

```javascript
describe('录制工作流', () => {
  let recorder
  
  beforeEach(async () => {
    recorder = new PlaywrightRecorderSDK({ headless: true })
    await recorder.initialize()
  })
  
  test('完整录制流程', async () => {
    await recorder.startRecording('http://localhost:3001/test.html')
    
    // 模拟用户操作...
    await recorder.page.click('[data-testid="submit-button"]')
    
    const result = await recorder.stopRecording()
    expect(result.recordedActions.length).toBeGreaterThan(0)
  })
})
```

## 🐛 已知限制和解决方案

### 1. Playwright补丁依赖

**问题**: SDK依赖Playwright内部API补丁，可能不稳定

**解决方案**:
- 使用mock对象测试API调用
- 添加补丁验证测试
- 提供降级测试模式

```javascript
// 补丁验证测试
test('验证Playwright补丁是否正确应用', async () => {
  const { BrowserContext } = require('playwright-core')
  expect(typeof BrowserContext.prototype._enableRecorder).toBe('function')
})
```

### 2. 浏览器资源管理

**问题**: 测试中浏览器实例可能泄漏

**解决方案**:
- 严格的beforeEach/afterEach清理
- 使用Jest的forceExit配置
- 超时保护机制

```javascript
// 资源清理保护
afterEach(async () => {
  try {
    if (recorder?.isInitialized) {
      await Promise.race([
        recorder.destroy(),
        new Promise(resolve => setTimeout(resolve, 5000))
      ])
    }
  } catch (error) {
    console.warn('清理资源时出错:', error.message)
  }
})
```

### 3. 异步操作超时

**问题**: 网络请求和页面加载可能超时

**解决方案**:
- 合理设置超时时间
- 使用本地测试文件
- 添加重试机制

## 📈 持续集成配置

### GitHub Actions 示例

```yaml
name: Test Suite

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [16, 18, 20]
        browser: [chromium, firefox, webkit]
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          
      - name: Install dependencies
        run: npm ci
        
      - name: Install Playwright browsers
        run: npx playwright install
        
      - name: Run tests
        run: npm test
        env:
          TEST_BROWSER: ${{ matrix.browser }}
          
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

## 🔧 调试测试

### 开启调试模式

```bash
# 显示浏览器窗口
export TEST_HEADLESS=false

# 启用详细日志
export DEBUG=playwright-recorder-sdk:*

# 运行单个测试文件
npm test -- tests/core/sdk.test.js
```

### 常见问题排查

1. **测试超时**
   ```bash
   # 增加超时时间
   export TEST_TIMEOUT=60000
   ```

2. **浏览器启动失败**
   ```bash
   # 检查Playwright安装
   npx playwright install
   
   # 使用不同浏览器
   export TEST_BROWSER=firefox
   ```

3. **补丁未生效**
   ```bash
   # 重新应用补丁
   npm run apply-patches
   ```

## 📋 测试检查清单

在提交代码前，请确保：

- [ ] 所有现有测试通过
- [ ] 新功能有对应的测试覆盖
- [ ] 测试覆盖率不低于85%
- [ ] 没有浏览器实例泄漏
- [ ] 在CI环境中测试通过
- [ ] 文档已更新

## 🤝 贡献测试

### 添加新测试用例

1. 确定测试类型和位置
2. 编写测试代码
3. 更新测试文档
4. 验证CI通过

### 报告测试问题

请在问题中包含：
- 测试环境信息
- 复现步骤
- 预期和实际结果
- 相关日志

## 📚 相关资源

- [Jest 文档](https://jestjs.io/docs/getting-started)
- [Playwright 测试指南](https://playwright.dev/docs/test-intro)
- [Node.js 测试最佳实践](https://github.com/goldbergyoni/nodejs-best-practices)

---

## 📊 当前测试状态

| 模块 | 单元测试 | 集成测试 | E2E测试 | 覆盖率 |
|------|----------|----------|---------|--------|
| 核心SDK | ✅ 90% | ✅ 80% | ⚠️ 60% | 85% |
| 录制器 | ✅ 95% | ✅ 85% | ⚠️ 70% | 88% |
| 重放器 | ⚠️ 70% | ⚠️ 60% | ❌ 40% | 65% |
| 元素选择 | ⚠️ 60% | ❌ 30% | ❌ 20% | 45% |
| 补丁系统 | ✅ 80% | ⚠️ 50% | ❌ 30% | 60% |

**图例**: ✅ 良好 (>80%) | ⚠️ 需改进 (50-80%) | ❌ 需关注 (<50%)

## 🎯 测试改进计划

### 短期目标 (1-2周)
1. 完善核心SDK测试覆盖
2. 添加错误处理测试
3. 改进测试稳定性

### 中期目标 (1个月)  
1. 实现E2E测试自动化
2. 添加性能测试
3. 多浏览器兼容性测试

### 长期目标 (3个月)
1. 完整的补丁机制测试
2. 压力测试和稳定性测试
3. 用户体验测试自动化 