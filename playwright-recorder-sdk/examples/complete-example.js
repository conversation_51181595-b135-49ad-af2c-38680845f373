/**
 * 完整功能示例
 * 覆盖Playwright Recorder SDK的所有功能，包括修复pick locator状态保持问题
 */

const PlaywrightRecorderSDK = require('../index')
const fs = require('fs').promises
const path = require('path')

class CompleteRecorderExample {
  constructor () {
    this.recorder = null
    this.selectedElements = []
    this.recordedActions = []
    this.outputDir = './complete-demo-output'
  }

  async initialize () {
    console.log('🚀 初始化完整功能演示...')

    this.recorder = new PlaywrightRecorderSDK({
      browser: 'chromium',
      headless: false,
      launchOptions: {
        args: ['--start-maximized'],
        slowMo: 100
      },
      contextOptions: {
        viewport: { width: 1920, height: 1080 },
        locale: 'zh-CN'
      },
      outputDir: this.outputDir,
      scriptFormat: 'javascript',
      enableTracing: true,
      enableVideo: false,
      timeout: 60000
    });

    // 设置所有事件监听器
    this.setupEventListeners();

    await this.recorder.initialize()
    console.log('✅ 录制器初始化完成')
  }

  setupEventListeners () {
    // 初始化事件
    this.recorder.on('initialized', () => {
      console.log('📺 录制器已初始化')
    })

    // 录制事件
    this.recorder.on('recordingStarted', (data) => {
      console.log('🎬 录制开始:', data.url)
    })

    this.recorder.on('recordingStopped', (data) => {
      console.log('🛑 录制停止, 共录制', data.actionsCount, '个动作')
    })

    this.recorder.on('actionRecorded', (action) => {
      console.log('📝 新动作:', action.type, action.selector || action.url)
      this.recordedActions.push(action)
    })

    // 元素选择事件
    this.recorder.on('elementSelected', (element) => {
      console.log('🎯 元素已选择:', {
        selector: element.selector,
        tagName: element.tagName,
        text: element.textContent?.substring(0, 30)
      })
      this.selectedElements.push(element)
    })

    this.recorder.on('elementPickerEnabled', () => {
      console.log('🔍 元素选择器已启用')
    })

    this.recorder.on('elementPickerDisabled', () => {
      console.log('🔍 元素选择器已禁用')
    })

    // 脚本重放事件
    this.recorder.on('replayStarted', (data) => {
      console.log('🔄 脚本重放开始')
    })

    this.recorder.on('replayCompleted', (result) => {
      console.log('✅ 脚本重放完成:', result.success ? '成功' : '失败')
    })

    this.recorder.on('replayFailed', (error) => {
      console.log('❌ 脚本重放失败:', error.message)
    })

    // 错误事件
    this.recorder.on('error', (error) => {
      console.error('💥 录制器错误:', error.message)
    })
  }

  async runCompleteDemo () {
    try {
      await this.initialize();

      console.log('\n🎭 开始完整功能演示...\n');

      // 1. 页面导航演示
      await this.demonstrateNavigation();

      // 2. 基础录制演示
      await this.demonstrateBasicRecording();

      // 3. 增强型元素选择器演示（修复状态保持问题）
      await this.demonstrateEnhancedElementPicker();

      // 4. 多格式脚本生成演示
      await this.demonstrateScriptGeneration();

      // 5. 高级录制功能演示
      await this.demonstrateAdvancedRecording();

      // 6. 脚本重放演示
      await this.demonstrateScriptReplay();

      // 7. 状态监控演示
      await this.demonstrateStatusMonitoring();

      // 8. 批量操作演示
      await this.demonstrateBatchOperations();

      // 9. 错误处理演示
      await this.demonstrateErrorHandling();

      // 10. 生成总结报告
      await this.generateSummaryReport();

      console.log('\n🎉 完整功能演示完成！');

    } catch (error) {
      console.error('💥 演示过程中发生错误:', error.message);
    } finally {
      if (this.recorder) {
        await this.recorder.destroy();
      }
    }
  }

  async demonstrateNavigation () {
    console.log('🌐 === 页面导航演示 ===');

    // 导航到测试页面
    const testUrl = 'https://playwright.dev';
    await this.recorder.navigateTo(testUrl);

    const currentUrl = await this.recorder.getCurrentUrl();
    console.log('📍 当前页面:', currentUrl);

    // 等待页面加载
    await this.sleep(2000);
  }

  async demonstrateBasicRecording () {
    console.log('\n🎬 === 基础录制演示 ===');

    // 开始录制
    await this.recorder.startRecording('https://example.com');

    console.log('📝 请在浏览器中进行基础操作（点击、输入等）...');
    console.log('⏰ 5秒后自动停止录制');

    await this.sleep(5000);

    // 停止录制
    const result = await this.recorder.stopRecording();
    console.log(`✅ 基础录制完成，共录制 ${result.recordedActions.length} 个动作`);
  }

  async demonstrateEnhancedElementPicker () {
    console.log('\n🎯 === 增强型元素选择器演示（修复状态保持问题）===');

    // 导航到有丰富内容的页面
    await this.recorder.navigateTo('https://playwright.dev');
    await this.sleep(2000);

    // 启用持续选择模式（修复状态保持问题）
    console.log('🔍 启用持续元素选择模式...');
    
    // 通过页面注入脚本启用持续模式
    await this.enableContinuousElementPicker();

    console.log('👆 请点击页面上的多个元素进行选择...');
    console.log('🔥 修复后的pick locator会保持激活状态，允许连续选择');
    console.log('⌨️  按ESC键可退出选择模式');
    console.log('⏰ 10秒后自动停止选择模式');

    // 等待用户交互
    await this.sleep(10000);

    // 手动禁用元素选择器
    await this.recorder.disableElementPicker();

    console.log(`✅ 元素选择完成，共选择了 ${this.selectedElements.length} 个元素:`);
    this.selectedElements.forEach((element, index) => {
      console.log(`  ${index + 1}. ${element.selector} (${element.tagName})`);
    });
  }

  async enableContinuousElementPicker () {
    // 注入修复脚本，启用持续选择模式
    const fixScript = `
      // 修复pick locator状态保持问题
      if (window.playwrightRecorderSDK && window.playwrightRecorderSDK.pickLocatorEnhancer) {
        // 启用持续模式和多选模式
        window.playwrightRecorderSDK.pickLocatorEnhancer.enable({
          continuousMode: true,
          multiSelect: true,
          showTooltip: true,
          highlightColor: '#4ecdc4',
          selectedColor: '#45b7d1'
        });
        
        // 重写点击处理，确保不会自动禁用
        const originalHandleClick = window.playwrightRecorderSDK.pickLocatorEnhancer._handleClick;
        window.playwrightRecorderSDK.pickLocatorEnhancer._handleClick = function(event) {
          if (!this.isActive) return;

          event.preventDefault();
          event.stopPropagation();

          const element = event.target;
          
          if (this._isOurElement(element)) {
            return;
          }

          // 选择元素但不禁用选择器
          this._selectElement(element);
          
          // 发送选择通知
          console.log('✅ 元素已选择，选择器保持激活状态');
        };
        
        console.log('🔧 Pick Locator 增强修复已应用');
      }
    `;

    await this.recorder.page.evaluate(fixScript);
    await this.recorder.enableElementPicker();
  }

  async demonstrateScriptGeneration () {
    console.log('\n📝 === 多格式脚本生成演示 ===');

    // 确保有录制的动作
    if (this.recordedActions.length === 0) {
      console.log('🎬 先录制一些动作用于脚本生成...');
      await this.recorder.startRecording('https://example.com');
      await this.sleep(3000);
      await this.recorder.stopRecording();
    }

    const formats = ['javascript', 'typescript', 'python'];
    
    for (const format of formats) {
      try {
        const script = this.recorder.getGeneratedScript(format);
        
        if (script && script.length > 0) {
          const extension = format === 'javascript' ? 'js' : 
                          format === 'typescript' ? 'ts' : 'py';
          
          const outputPath = path.join(this.outputDir, `generated_script.${extension}`);
          await fs.mkdir(path.dirname(outputPath), { recursive: true });
          await fs.writeFile(outputPath, script);
          
          console.log(`💾 ${format} 脚本已生成: ${outputPath} (${script.length} 字符)`);
        } else {
          console.log(`⚠️  ${format} 脚本生成失败或为空`);
        }
      } catch (error) {
        console.error(`❌ ${format} 脚本生成出错:`, error.message);
      }
    }
  }

  async demonstrateAdvancedRecording () {
    console.log('\n🎛️ === 高级录制功能演示 ===');

    // 演示暂停和恢复
    await this.recorder.startRecording('https://playwright.dev');
    console.log('🎬 录制已开始，2秒后暂停...');

    await this.sleep(2000);
    await this.recorder.pauseRecording();
    console.log('⏸️  录制已暂停，2秒后恢复...');

    await this.sleep(2000);
    await this.recorder.resumeRecording();
    console.log('▶️  录制已恢复，3秒后停止...');

    await this.sleep(3000);
    await this.recorder.stopRecording();
    console.log('🛑 高级录制演示完成');
  }

  async demonstrateScriptReplay () {
    console.log('\n🔄 === 脚本重放演示 ===');

    // 定义测试动作
    const testActions = [
      { type: 'navigate', url: 'https://example.com' },
      { type: 'wait', timeout: 1000 },
      { type: 'click', selector: 'h1' },
      { type: 'wait', timeout: 500 }
    ];

    console.log('🔄 开始重放测试脚本...');

    try {
      const result = await this.recorder.replayScript(testActions, {
        slowMo: 1000,
        timeout: 30000
      });

      console.log('✅ 脚本重放结果:', result.success ? '成功' : '失败');
    } catch (error) {
      console.error('❌ 脚本重放失败:', error.message);
    }
  }

  async demonstrateStatusMonitoring () {
    console.log('\n📊 === 状态监控演示 ===');

    // 启动状态监控
    const statusInterval = setInterval(() => {
      const status = this.recorder.getRecordingStatus();
      console.log('📊 当前状态:', {
        录制中: status.isRecording,
        检查模式: status.isInspectMode,
        当前URL: status.currentUrl?.substring(0, 50) + '...',
        动作数量: status.actionsCount,
        选中元素: this.selectedElements.length
      });
    }, 3000);

    // 进行一些操作来演示状态变化
    await this.recorder.startRecording('https://playwright.dev');
    await this.sleep(2000);
    await this.recorder.enableElementPicker();
    await this.sleep(2000);
    await this.recorder.disableElementPicker();
    await this.recorder.stopRecording();

    clearInterval(statusInterval);
    console.log('📊 状态监控演示完成');
  }

  async demonstrateBatchOperations () {
    console.log('\n📦 === 批量操作演示 ===');

    const testUrls = [
      'https://example.com',
      'https://playwright.dev',
      'https://github.com'
    ];

    for (const [index, url] of testUrls.entries()) {
      console.log(`📦 批量操作 ${index + 1}/${testUrls.length}: ${url}`);

      try {
        await this.recorder.navigateTo(url);
        await this.sleep(1000);

        // 获取页面信息
        const currentUrl = await this.recorder.getCurrentUrl();
        const status = this.recorder.getRecordingStatus();
        
        console.log(`  ✅ 页面加载完成: ${currentUrl}`);
        console.log(`  📊 状态: 录制中=${status.isRecording}, 检查模式=${status.isInspectMode}`);

      } catch (error) {
        console.error(`  ❌ 批量操作失败 ${url}:`, error.message);
      }
    }

    console.log('📦 批量操作演示完成');
  }

  async demonstrateErrorHandling () {
    console.log('\n🛡️ === 错误处理演示 ===');

    try {
      // 故意触发一些错误来演示错误处理
      console.log('🧪 测试无效URL导航...');
      await this.recorder.navigateTo('invalid://url');
    } catch (error) {
      console.log('✅ 预期错误已捕获:', error.message);
    }

    try {
      console.log('🧪 测试无效选择器...');
      await this.recorder.getElementInfo('invalid>>selector');
    } catch (error) {
      console.log('✅ 预期错误已捕获:', error.message);
    }

    console.log('🛡️ 错误处理演示完成');
  }

  async generateSummaryReport () {
    console.log('\n📋 === 生成总结报告 ===');

    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalRecordedActions: this.recordedActions.length,
        totalSelectedElements: this.selectedElements.length,
        demonstratedFeatures: [
          '页面导航',
          '基础录制',
          '增强型元素选择器（已修复状态保持问题）',
          '多格式脚本生成',
          '高级录制功能',
          '脚本重放',
          '状态监控',
          '批量操作',
          '错误处理'
        ]
      },
      recordedActions: this.recordedActions,
      selectedElements: this.selectedElements.map(el => ({
        selector: el.selector,
        tagName: el.tagName,
        textContent: el.textContent
      })),
      finalStatus: this.recorder.getRecordingStatus()
    };

    const reportPath = path.join(this.outputDir, 'demo-report.json');
    await fs.mkdir(path.dirname(reportPath), { recursive: true });
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

    console.log(`📋 总结报告已生成: ${reportPath}`);
    console.log('📊 演示统计:');
    console.log(`  - 录制动作: ${report.summary.totalRecordedActions} 个`);
    console.log(`  - 选中元素: ${report.summary.totalSelectedElements} 个`);
    console.log(`  - 演示功能: ${report.summary.demonstratedFeatures.length} 项`);
  }

  async sleep (ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 使用说明函数
function printUsageInstructions () {
  console.log(`
🎭 Playwright Recorder SDK - 完整功能演示
===============================================

本示例演示了SDK的所有核心功能，包括：

🔧 修复的功能:
• Pick Locator状态保持问题 - 现在支持持续选择模式
• 元素选择器不会在点击后自动禁用
• 支持多选模式和连续选择

🎯 主要功能:
• 页面导航和状态管理
• 录制开始/停止/暂停/恢复
• 增强型元素选择器（已修复）
• 多格式脚本生成 (JS/TS/Python)
• 脚本重放和执行
• 实时状态监控
• 批量操作处理
• 完善的错误处理

🎮 交互说明:
• 在元素选择模式下，可以连续点击多个元素
• 按ESC键退出选择模式
• 所有操作都会显示详细的控制台输出
• 生成的文件保存在 ./complete-demo-output/ 目录

▶️  运行演示: node complete-example.js
`);
}

// 主函数
async function main () {
  // 检查是否显示帮助
  if (process.argv.includes('--help') || process.argv.includes('-h')) {
    printUsageInstructions();
    return;
  }

  console.log('🎭 启动Playwright Recorder SDK完整功能演示...\n');
  
  const demo = new CompleteRecorderExample();
  await demo.runCompleteDemo();
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 未处理的Promise拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('💥 未捕获的异常:', error);
  process.exit(1);
});

// 优雅退出
process.on('SIGINT', () => {
  console.log('\n🛑 收到中断信号，正在清理...');
  process.exit(0);
});

// 导出以供其他模块使用
module.exports = { CompleteRecorderExample, main };

// 如果直接运行此文件
if (require.main === module) {
  main().catch(error => {
    console.error('💥 演示执行失败:', error);
    process.exit(1);
  });
} 