#!/usr/bin/env node

/**
 * 快速启动脚本 - Playwright Recorder SDK 完整演示
 * 使用方法: npm run demo 或 node run-demo.js
 */

console.log(`
🎭 Playwright Recorder SDK - 完整功能演示
=========================================

🔧 已修复的关键问题:
✅ Pick Locator 状态保持问题
✅ 元素选择器持续激活模式
✅ 支持多元素连续选择

🎯 演示功能:
• 页面导航和基础录制
• 增强型元素选择器（已修复）
• 多格式脚本生成 (JS/TS/Python)
• 高级录制功能和脚本重放
• 状态监控和批量操作
• 错误处理和完整报告

⚡ 启动演示中...
`)

// 延迟一下让用户看到说明
setTimeout(() => {
  require('./complete-example.js')
}, 1000) 