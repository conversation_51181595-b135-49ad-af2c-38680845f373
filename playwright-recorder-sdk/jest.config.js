module.exports = {
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  collectCoverageFrom: [
    'lib/**/*.js',
    'index.js',
    '!lib/client-scripts/**',
    '!**/node_modules/**'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: [
    'text',
    'lcov',
    'html'
  ],
  testMatch: [
    '**/tests/**/*.test.js',
    '**/tests/**/*.spec.js'
  ],
  testTimeout: 60000,
  verbose: true,
  collectCoverage: false,
  bail: false,
  maxWorkers: 1, // 串行执行测试，避免浏览器冲突
  forceExit: true,
  detectOpenHandles: true,
  globals: {
    TEST_TIMEOUT: 30000
  }
} 