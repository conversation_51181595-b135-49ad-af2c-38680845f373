/**
 * Playwright Recorder SDK 核心功能测试
 */

const PlaywrightRecorderSDK = require('../../index')
const path = require('path')

describe('PlaywrightRecorderSDK Core Tests', () => {
  let recorder

  beforeEach(() => {
    recorder = new PlaywrightRecorderSDK({
      browser: 'chromium',
      headless: true,
      timeout: 10000
    })
  })

  afterEach(async () => {
    if (recorder && recorder.isInitialized) {
      await recorder.destroy()
    }
  })

  describe('初始化和清理', () => {
    test('应该能够创建SDK实例', () => {
      expect(recorder).toBeInstanceOf(PlaywrightRecorderSDK)
      expect(recorder.isInitialized).toBe(false)
    })

    test('应该能够成功初始化', async () => {
      await recorder.initialize()
      
      expect(recorder.isInitialized).toBe(true)
      expect(recorder.browser).toBeTruthy()
      expect(recorder.context).toBeTruthy()
      expect(recorder.page).toBeTruthy()
    })

    test('应该能够正确清理资源', async () => {
      await recorder.initialize()
      await recorder.destroy()
      
      expect(recorder.isInitialized).toBe(false)
    })

    test('重复初始化应该抛出错误', async () => {
      await recorder.initialize()
      
      await expect(recorder.initialize()).rejects.toThrow()
    })
  })

  describe('录制控制', () => {
    beforeEach(async () => {
      await recorder.initialize()
    })

    test('应该能够开始录制', async () => {
      const testUrl = `file://${path.join(__dirname, '..', 'fixtures', 'test.html')}`
      
      const result = await recorder.startRecording(testUrl)
      
      expect(result.success).toBe(true)
      expect(result.url).toBe(testUrl)
      expect(recorder.isRecording).toBe(true)
    })

    test('应该能够暂停录制', async () => {
      const testUrl = `file://${path.join(__dirname, '..', 'fixtures', 'test.html')}`
      await recorder.startRecording(testUrl)
      
      const result = await recorder.pauseRecording()
      
      expect(result.success).toBe(true)
      expect(recorder.isRecording).toBe(false)
    })

    test('应该能够恢复录制', async () => {
      const testUrl = `file://${path.join(__dirname, '..', 'fixtures', 'test.html')}`
      await recorder.startRecording(testUrl)
      await recorder.pauseRecording()
      
      const result = await recorder.resumeRecording()
      
      expect(result.success).toBe(true)
      expect(recorder.isRecording).toBe(true)
    })

    test('应该能够停止录制', async () => {
      const testUrl = `file://${path.join(__dirname, '..', 'fixtures', 'test.html')}`
      await recorder.startRecording(testUrl)
      
      const result = await recorder.stopRecording()
      
      expect(result.success).toBe(true)
      expect(result.generatedScripts).toBeDefined()
      expect(result.recordedActions).toBeDefined()
      expect(recorder.isRecording).toBe(false)
    })
  })

  describe('脚本生成', () => {
    beforeEach(async () => {
      await recorder.initialize()
    })

    test('应该能够获取JavaScript脚本', async () => {
      const script = recorder.getGeneratedScript('javascript')
      expect(typeof script).toBe('string')
    })

    test('应该能够获取所有格式的脚本', async () => {
      const scripts = recorder.getAllGeneratedScripts()
      
      expect(scripts).toHaveProperty('javascript')
      expect(scripts).toHaveProperty('typescript')
      expect(scripts).toHaveProperty('python')
      expect(scripts).toHaveProperty('java')
      expect(scripts).toHaveProperty('csharp')
      expect(scripts).toHaveProperty('jsonl')
    })

    test('应该能够获取录制的动作', async () => {
      const actions = recorder.getRecordedActions()
      expect(Array.isArray(actions)).toBe(true)
    })
  })

  describe('元素选择器', () => {
    beforeEach(async () => {
      await recorder.initialize()
      const testUrl = `file://${path.join(__dirname, '..', 'fixtures', 'test.html')}`
      await recorder.navigateTo(testUrl)
    })

    test('应该能够启用元素选择器', async () => {
      await recorder.enableElementPicker()
      
      expect(recorder.isInspectMode).toBe(true)
    })

    test('应该能够禁用元素选择器', async () => {
      await recorder.enableElementPicker()
      await recorder.disableElementPicker()
      
      expect(recorder.isInspectMode).toBe(false)
    })

    test('应该能够切换元素选择器状态', async () => {
      const initialState = recorder.isInspectMode
      
      await recorder.toggleElementPicker()
      expect(recorder.isInspectMode).toBe(!initialState)
      
      await recorder.toggleElementPicker()
      expect(recorder.isInspectMode).toBe(initialState)
    })

    test('应该能够获取元素信息', async () => {
      const elementInfo = await recorder.getElementInfo('[data-testid="page-title"]')
      
      expect(elementInfo).toBeDefined()
      // 注意：由于补丁机制的限制，某些功能可能不完全可用
    })
  })

  describe('页面导航', () => {
    beforeEach(async () => {
      await recorder.initialize()
    })

    test('应该能够导航到指定URL', async () => {
      const testUrl = `file://${path.join(__dirname, '..', 'fixtures', 'test.html')}`
      
      await recorder.navigateTo(testUrl)
      
      const currentUrl = await recorder.getCurrentUrl()
      expect(currentUrl).toBe(testUrl)
    })

    test('应该能够获取当前URL', async () => {
      const testUrl = `file://${path.join(__dirname, '..', 'fixtures', 'test.html')}`
      await recorder.navigateTo(testUrl)
      
      const currentUrl = await recorder.getCurrentUrl()
      
      expect(currentUrl).toBe(testUrl)
    })
  })

  describe('浏览器访问器', () => {
    beforeEach(async () => {
      await recorder.initialize()
    })

    test('应该能够获取浏览器实例', () => {
      const browser = recorder.getBrowser()
      expect(browser).toBeTruthy()
    })

    test('应该能够获取上下文实例', () => {
      const context = recorder.getContext()
      expect(context).toBeTruthy()
    })

    test('应该能够获取页面实例', () => {
      const page = recorder.getPage()
      expect(page).toBeTruthy()
    })
  })

  describe('录制状态', () => {
    beforeEach(async () => {
      await recorder.initialize()
    })

    test('应该能够获取录制状态', () => {
      const status = recorder.getRecordingStatus()
      
      expect(status).toHaveProperty('isInitialized')
      expect(status).toHaveProperty('isRecording')
      expect(status).toHaveProperty('currentMode')
      expect(status).toHaveProperty('actionsCount')
    })
  })

  describe('事件系统', () => {
    beforeEach(async () => {
      await recorder.initialize()
    })

    test('应该能够监听初始化事件', async () => {
      const newRecorder = new PlaywrightRecorderSDK({ headless: true })
      
      let eventFired = false
      newRecorder.on('initialized', () => {
        eventFired = true
      })
      
      await newRecorder.initialize()
      
      expect(eventFired).toBe(true)
      
      await newRecorder.destroy()
    })

    test('应该能够监听录制事件', async () => {
      const testUrl = `file://${path.join(__dirname, '..', 'fixtures', 'test.html')}`
      
      let startEventFired = false
      let stopEventFired = false
      
      recorder.on('recording:started', () => {
        startEventFired = true
      })
      
      recorder.on('recording:stopped', () => {
        stopEventFired = true
      })
      
      await recorder.startRecording(testUrl)
      await recorder.stopRecording()
      
      expect(startEventFired).toBe(true)
      expect(stopEventFired).toBe(true)
    })

    test('应该能够监听错误事件', async () => {
      let errorEventFired = false
      
      recorder.on('error', () => {
        errorEventFired = true
      })
      
      // 尝试执行一个会失败的操作
      try {
        await recorder.navigateTo('invalid-url')
      } catch (error) {
        // 预期错误
      }
      
      // 根据实际实现，错误事件可能会或不会被触发
    })
  })
}) 