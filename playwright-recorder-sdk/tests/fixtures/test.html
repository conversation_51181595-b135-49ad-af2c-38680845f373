<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Playwright Recorder SDK 测试页面</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 40px;
      background-color: #f5f5f5;
    }
    
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    h1 {
      color: #333;
      text-align: center;
    }
    
    .form-group {
      margin-bottom: 20px;
    }
    
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    
    input, textarea, select {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 16px;
    }
    
    button {
      background-color: #007cba;
      color: white;
      padding: 12px 24px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-right: 10px;
    }
    
    button:hover {
      background-color: #005a87;
    }
    
    .button-group {
      text-align: center;
      margin-top: 20px;
    }
    
    .status {
      margin-top: 20px;
      padding: 15px;
      border-radius: 4px;
      display: none;
    }
    
    .status.success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .status.error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .test-elements {
      margin-top: 30px;
      padding-top: 30px;
      border-top: 1px solid #eee;
    }
    
    .checkbox-group {
      display: flex;
      gap: 20px;
      margin-top: 10px;
    }
    
    .checkbox-item {
      display: flex;
      align-items: center;
      gap: 5px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 data-testid="page-title">Playwright Recorder SDK 测试页面</h1>
    
    <form id="test-form" data-testid="test-form">
      <div class="form-group">
        <label for="username">用户名:</label>
        <input type="text" id="username" name="username" data-testid="username-input" placeholder="请输入用户名">
      </div>
      
      <div class="form-group">
        <label for="email">邮箱:</label>
        <input type="email" id="email" name="email" data-testid="email-input" placeholder="请输入邮箱地址">
      </div>
      
      <div class="form-group">
        <label for="password">密码:</label>
        <input type="password" id="password" name="password" data-testid="password-input" placeholder="请输入密码">
      </div>
      
      <div class="form-group">
        <label for="message">留言:</label>
        <textarea id="message" name="message" data-testid="message-textarea" rows="4" placeholder="请输入您的留言"></textarea>
      </div>
      
      <div class="form-group">
        <label for="country">国家:</label>
        <select id="country" name="country" data-testid="country-select">
          <option value="">请选择国家</option>
          <option value="china">中国</option>
          <option value="usa">美国</option>
          <option value="japan">日本</option>
          <option value="uk">英国</option>
        </select>
      </div>
      
      <div class="form-group">
        <label>兴趣爱好:</label>
        <div class="checkbox-group">
          <div class="checkbox-item">
            <input type="checkbox" id="coding" name="hobbies" value="coding" data-testid="hobby-coding">
            <label for="coding">编程</label>
          </div>
          <div class="checkbox-item">
            <input type="checkbox" id="reading" name="hobbies" value="reading" data-testid="hobby-reading">
            <label for="reading">阅读</label>
          </div>
          <div class="checkbox-item">
            <input type="checkbox" id="music" name="hobbies" value="music" data-testid="hobby-music">
            <label for="music">音乐</label>
          </div>
        </div>
      </div>
      
      <div class="button-group">
        <button type="submit" data-testid="submit-button">提交表单</button>
        <button type="button" id="clear-button" data-testid="clear-button">清空表单</button>
        <button type="button" id="test-click" data-testid="test-click">测试点击</button>
      </div>
    </form>
    
    <div id="status" class="status" data-testid="status-message"></div>
    
    <div class="test-elements">
      <h2>测试元素</h2>
      <button id="dynamic-button" data-testid="dynamic-button">动态按钮</button>
      <div id="hover-target" data-testid="hover-target" style="padding: 20px; background: #e9ecef; margin: 10px 0; border-radius: 4px;">
        鼠标悬停目标区域
      </div>
      <a href="#test" id="test-link" data-testid="test-link">测试链接</a>
    </div>
  </div>

  <script>
    // 表单提交事件
    document.getElementById('test-form').addEventListener('submit', function(e) {
      e.preventDefault();
      
      const status = document.getElementById('status');
      status.className = 'status success';
      status.style.display = 'block';
      status.textContent = '表单提交成功！';
      
      setTimeout(() => {
        status.style.display = 'none';
      }, 3000);
    });
    
    // 清空表单
    document.getElementById('clear-button').addEventListener('click', function() {
      document.getElementById('test-form').reset();
      
      const status = document.getElementById('status');
      status.className = 'status';
      status.style.display = 'block';
      status.textContent = '表单已清空';
      
      setTimeout(() => {
        status.style.display = 'none';
      }, 2000);
    });
    
    // 测试点击
    document.getElementById('test-click').addEventListener('click', function() {
      alert('测试点击成功！');
    });
    
    // 动态按钮
    document.getElementById('dynamic-button').addEventListener('click', function() {
      this.textContent = this.textContent === '动态按钮' ? '已点击' : '动态按钮';
      this.style.backgroundColor = this.style.backgroundColor === 'green' ? '' : 'green';
    });
    
    // 悬停效果
    document.getElementById('hover-target').addEventListener('mouseenter', function() {
      this.style.backgroundColor = '#007cba';
      this.style.color = 'white';
      this.textContent = '鼠标已悬停！';
    });
    
    document.getElementById('hover-target').addEventListener('mouseleave', function() {
      this.style.backgroundColor = '#e9ecef';
      this.style.color = 'black';
      this.textContent = '鼠标悬停目标区域';
    });
    
    // 链接点击事件
    document.getElementById('test-link').addEventListener('click', function(e) {
      e.preventDefault();
      console.log('测试链接被点击');
    });
  </script>
</body>
</html> 