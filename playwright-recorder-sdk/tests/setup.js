/**
 * 测试环境配置
 */

const { chromium } = require('playwright')

// Jest 超时设置
jest.setTimeout(60000)

// 全局测试配置
global.testConfig = {
  browser: 'chromium',
  headless: true,
  timeout: 30000,
  testServer: 'http://localhost:3001'
}

// 测试前后钩子
beforeAll(async () => {
  console.log('🚀 开始测试环境初始化...')

  // 启动测试服务器
  if (process.env.START_TEST_SERVER !== 'false') {
    await startTestServer()
  }
})

afterAll(async () => {
  console.log('🧹 清理测试环境...')

  // 停止测试服务器
  if (global.testServer) {
    await global.testServer.close()
  }
})

// 启动简单的测试服务器
async function startTestServer () {
  const express = require('express')
  const path = require('path')

  const app = express()
  const port = 3001

  // 提供静态测试页面
  app.use(express.static(path.join(__dirname, 'fixtures')))

  // 测试API端点
  app.get('/api/test', (req, res) => {
    res.json({ message: 'Test API working', timestamp: Date.now() })
  })

  global.testServer = app.listen(port, () => {
    console.log(`✅ 测试服务器启动: http://localhost:${port}`)
  })
}

// 测试工具函数
global.testUtils = {
  // 创建测试页面
  createTestPage: async (content) => {
    const browser = await chromium.launch({ headless: true })
    const context = await browser.newContext()
    const page = await context.newPage()

    await page.setContent(content)

    return { browser, context, page }
  },

  // 等待元素
  waitForElement: async (page, selector, timeout = 5000) => {
    return await page.waitForSelector(selector, { timeout })
  },

  // 清理浏览器
  cleanup: async (browser) => {
    if (browser) {
      await browser.close()
    }
  }
} 