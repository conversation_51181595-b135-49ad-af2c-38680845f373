/**
 * 简单测试全局 Playwright 回放 API
 */

console.log('🧪 开始测试全局 Playwright 回放 API');

// 1. 加载 playwright-core 以触发 patch
console.log('📦 加载 playwright-core...');
require('playwright-core');

// 2. 检查全局 API 是否存在
console.log('🔍 检查全局 API...');
if (global.playwrightReplayAPI) {
  console.log('✅ 全局 API 存在！');
  
  const api = global.playwrightReplayAPI;
  console.log('📊 API 信息:');
  console.log('  - 版本:', api.version);
  console.log('  - 是否为补丁API:', api.isPatchedAPI);
  console.log('  - 补丁版本:', api.patchVersion);
  console.log('  - 可用函数:', Object.keys(api).filter(key => typeof api[key] === 'function'));
  
  // 3. 测试核心函数
  console.log('\n🔧 测试核心函数...');
  
  // 测试 buildFullSelector
  if (typeof api.buildFullSelector === 'function') {
    const framePath = ['iframe#main', 'iframe.content'];
    const selector = 'button.submit';
    const fullSelector = api.buildFullSelector(framePath, selector);
    console.log('✅ buildFullSelector 工作正常');
    console.log('  输入:', { framePath, selector });
    console.log('  输出:', fullSelector);
  } else {
    console.log('❌ buildFullSelector 不可用');
  }
  
  // 测试 createActionInContext
  if (typeof api.createActionInContext === 'function') {
    const actionInContext = api.createActionInContext('page', [], {
      name: 'click',
      selector: 'button',
      button: 'left',
      modifiers: 0,
      clickCount: 1
    });
    console.log('✅ createActionInContext 工作正常');
    console.log('  创建的对象结构正确:', 
      actionInContext.frame && 
      actionInContext.action && 
      typeof actionInContext.startTime === 'number'
    );
  } else {
    console.log('❌ createActionInContext 不可用');
  }
  
  // 测试 toClickOptions
  if (typeof api.toClickOptions === 'function') {
    const clickAction = {
      button: 'right',
      modifiers: 1,
      clickCount: 2,
      position: { x: 100, y: 200 }
    };
    const options = api.toClickOptions(clickAction);
    console.log('✅ toClickOptions 工作正常');
    console.log('  输入:', clickAction);
    console.log('  输出:', options);
  } else {
    console.log('❌ toClickOptions 不可用');
  }
  
  console.log('\n🎉 基础测试完成！');
  console.log('📝 全局 API 可以正常使用');
  
} else {
  console.log('❌ 全局 API 不存在！');
  console.log('可能的原因:');
  console.log('1. patch 没有正确应用');
  console.log('2. playwright-core 版本不兼容');
  console.log('3. Node.js 版本问题');
}

console.log('\n✨ 测试结束');
