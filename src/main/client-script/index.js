/**
 * 客户端增强脚本生成器 - 主入口
 * 生成注入到录制页面中的纯 JavaScript 代码
 * 提供元素选择、高亮显示和通信功能
 *
 * 注意：此文件生成的是纯浏览器 JavaScript 代码，不能使用 Node.js 模块
 */

const HighlightManager = require('./highlight-manager');
const SelectorGenerator = require('./selector-generator');
const ElementInfo = require('./element-info');
const ElectronBridge = require('./electron-bridge');
const PickLocatorEnhancer = require('./pick-locator-enhancer');
const RecordActionInterceptor = require('./record-action-interceptor');
const DebugUtils = require('./debug-utils');

/**
 * 生成客户端脚本代码
 * @returns {string} 客户端脚本代码
 */
function generateClientScript() {
  return `
    (function() {
      'use strict';

      // 防止重复执行 - 使用全局标记
      if (window.__playwrightRecorderEnhanced) {
        console.log('🔄 增强录制器脚本已存在，跳过重复初始化');
        return;
      }
      
      // 标记脚本已执行
      window.__playwrightRecorderEnhanced = true;

      // 全局状态
      let isInspectMode = false;
      let isRecordingMode = true;
      let selectedElement = null;
      let highlightManager = null;

      ${HighlightManager.getCode()}
      ${SelectorGenerator.getCode()}
      ${ElementInfo.getCode()}
      ${ElectronBridge.getCode()}
      ${PickLocatorEnhancer.getCode()}
      ${RecordActionInterceptor.getCode()}
      ${DebugUtils.getCode()}

      // 初始化
      function initialize() {
        initializeHighlightManager();
        enhancePlaywrightPickLocator();
        setupRecordActionInterception();

        console.log('🌉 增强录制器通信桥接已建立');
        console.log('💡 使用 Ctrl+点击 选择元素，或使用 Playwright 原生 pick locator');
        console.log('🎯 Pick locator 现在支持持续选择模式');
        console.log('🎬 Playwright 动作拦截已启用，可实时捕获录制动作');
        console.log('📋 增强动作拦截已启用，可构建 Playwright 代码');
        console.log('🎬 RecordAction 拦截已启用，可捕获录制动作并获取详细元素信息');
        console.log('🔧 在控制台中运行 debugPlaywrightRecorder() 查看状态');
        console.log('🧪 运行 testActionInterception() 测试动作拦截功能');
        console.log('📋 运行 testEnhancedActionInterception() 测试增强拦截功能');
        console.log('🧪 运行 testRecordActionInterception() 测试录制动作拦截功能');
        console.log('🧹 运行 clearActionHistory() 清理动作历史');
        console.log('📄 运行 getCurrentCode() 获取当前生成的代码');

        // 延迟检查 Playwright 状态
        setTimeout(() => {
          console.log('🔍 延迟检查 Playwright 状态:');
          window.debugPlaywrightRecorder();
        }, 2000);
      }

      // 页面加载完成后的初始化
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
      } else {
        initialize();
      }

    })();
  `;
}

module.exports = {
  generateClientScript
};
