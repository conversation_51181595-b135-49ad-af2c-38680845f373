/**
 * Record Action 拦截器模块
 * 负责拦截 __pw_recorderRecordAction 方法，获取元素详细信息并发送到 Electron
 */

function getCode() {
  return `
    // 拦截 __pw_recorderRecordAction 方法
    function setupRecordActionInterception() {
      console.log('🎬 开始设置 __pw_recorderRecordAction 拦截');
      console.log('🔍 检查 window.__pw_recorderRecordAction 是否存在:', !!window.__pw_recorderRecordAction);

      // 存储原始函数的引用
      let originalRecordAction = null;
      let isRecordActionInterceptionSetup = false;

      // 设置拦截的核心函数
      function setupRecordActionInterceptionCore() {
        if (isRecordActionInterceptionSetup) {
          console.log('⚠️ RecordAction 拦截已经设置，跳过重复设置');
          return;
        }

        originalRecordAction = window.__pw_recorderRecordAction;
        console.log('📝 保存原始 __pw_recorderRecordAction 函数:', !!originalRecordAction);

        // 创建我们的拦截函数
        const interceptedRecordAction = async function(actionInfo) {
          console.log('🎯 Playwright 录制动作被拦截:', actionInfo);
          
          // 获取选择器
          const selector = actionInfo.selector;
          console.log('🔍 提取的选择器:', selector);

          // 安全地尝试获取元素信息
          let element = null;
          let detailedInfo = null;

          try {
            // 尝试使用 Playwright 的 $ 方法获取元素
            if (window.playwright && window.playwright.$ && selector) {
              element = await window.playwright.$(selector);
              console.log('✅ 通过 playwright.$ 找到元素:', !!element);
            }

            // 如果 Playwright 方法失败，尝试使用原生 DOM 方法
            if (!element && selector) {
              try {
                // 尝试使用 CSS 选择器
                element = document.querySelector(selector);
                console.log('✅ 通过 document.querySelector 找到元素:', !!element);
              } catch (e) {
                console.log('⚠️ CSS 选择器查找失败:', e.message);
                
                // 尝试使用 XPath
                if (selector.startsWith('//') || selector.startsWith('xpath=')) {
                  try {
                    const xpathSelector = selector.replace(/^xpath=/, '');
                    const result = document.evaluate(xpathSelector, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
                    element = result.singleNodeValue;
                    console.log('✅ 通过 XPath 找到元素:', !!element);
                  } catch (xpathError) {
                    console.log('⚠️ XPath 查找失败:', xpathError.message);
                  }
                }
              }
            }

            // 获取详细的元素信息
            if (element) {
              detailedInfo = getElementInfo(element);
              console.log('🎯 获取到的详细元素信息:', detailedInfo);
            }

            // 发送详细信息到 Electron
            const recordActionData = {
              actionInfo: actionInfo,
              selector: selector,
              element: element ? {
                tagName: element.tagName,
                id: element.id,
                className: element.className,
                textContent: element.textContent,
                innerText: element.innerText,
                value: element.value,
                href: element.href,
                src: element.src
              } : null,
              detailedInfo: detailedInfo,
              timestamp: Date.now()
            };

            console.log('📤 发送录制动作数据到 Electron:', recordActionData);
            
            // 发送到 Electron
            window.electronRecorderBridge.sendToElectron('recordActionIntercepted', recordActionData);

          } catch (error) {
            console.error('❌ 处理录制动作时出错:', error);
            console.log('📝 原始动作信息:', actionInfo);
            
            // 即使出错也要发送基本信息
            window.electronRecorderBridge.sendToElectron('recordActionIntercepted', {
              actionInfo: actionInfo,
              selector: selector,
              element: null,
              detailedInfo: null,
              error: error.message,
              timestamp: Date.now()
            });
          }

          // 调用原始函数
          if (originalRecordAction) {
            return originalRecordAction(actionInfo);
          }
        };

        // 标记拦截函数，便于识别
        interceptedRecordAction._isCustomInterception = true;

        // 替换原始函数
        window.__pw_recorderRecordAction = interceptedRecordAction;
        isRecordActionInterceptionSetup = true;

        console.log('✅ __pw_recorderRecordAction 拦截设置完成');
      }

      // 尝试立即设置拦截
      function attemptRecordActionInterception() {
        if (window.__pw_recorderRecordAction && !window.__pw_recorderRecordAction._isCustomInterception) {
          console.log('✅ 立即设置 __pw_recorderRecordAction 拦截');
          setupRecordActionInterceptionCore();
          return true;
        }
        return false;
      }

      // 立即尝试设置拦截
      const setupSuccess = attemptRecordActionInterception();

      // 如果立即设置失败，使用多种策略等待
      if (!setupSuccess) {
        console.log('⏳ __pw_recorderRecordAction 还不存在，设置监听器等待...');

        // 使用定时器检查
        let checkCount = 0;
        const checkInterval = setInterval(() => {
          checkCount++;

          if (window.__pw_recorderRecordAction && !window.__pw_recorderRecordAction._isCustomInterception) {
            console.log(\`✅ 在第 \${checkCount} 次检查时发现 __pw_recorderRecordAction\`);
            setupRecordActionInterceptionCore();
            clearInterval(checkInterval);
          } else if (checkCount > 100) { // 10秒后停止检查
            console.log('❌ 超时：__pw_recorderRecordAction 未找到');
            clearInterval(checkInterval);
          }
        }, 100);

        // 使用 MutationObserver 监听 window 对象的变化
        const observer = new MutationObserver(() => {
          if (window.__pw_recorderRecordAction && !window.__pw_recorderRecordAction._isCustomInterception) {
            console.log('✅ 通过 MutationObserver 发现 __pw_recorderRecordAction');
            setupRecordActionInterceptionCore();
            observer.disconnect();
          }
        });

        // 监听 window 对象的属性变化
        if (typeof window.addEventListener === 'function') {
          const propertyChecker = () => {
            if (window.__pw_recorderRecordAction && !window.__pw_recorderRecordAction._isCustomInterception) {
              console.log('✅ 通过属性检查发现 __pw_recorderRecordAction');
              setupRecordActionInterceptionCore();
              window.removeEventListener('load', propertyChecker);
              window.removeEventListener('DOMContentLoaded', propertyChecker);
            }
          };

          window.addEventListener('load', propertyChecker);
          window.addEventListener('DOMContentLoaded', propertyChecker);
        }

        // 使用 Proxy 监听 window 对象的属性访问
        try {
          const originalWindow = window;
          const windowProxy = new Proxy(originalWindow, {
            set: function(target, property, value) {
              if (property === '__pw_recorderRecordAction' && !value._isCustomInterception) {
                console.log('✅ 通过 Proxy 发现 __pw_recorderRecordAction');
                target[property] = value;
                setTimeout(() => setupRecordActionInterceptionCore(), 0);
                return true;
              }
              target[property] = value;
              return true;
            }
          });
        } catch (proxyError) {
          console.log('⚠️ Proxy 设置失败:', proxyError.message);
        }

      } else {
        console.log('🎉 __pw_recorderRecordAction 拦截立即设置完成');
      }
    }

    // 测试函数
    function testRecordActionInterception() {
      console.log('🧪 测试 RecordAction 拦截功能');
      console.log('🔍 当前 __pw_recorderRecordAction 状态:');
      console.log('   存在:', !!window.__pw_recorderRecordAction);
      console.log('   是否为自定义拦截:', !!window.__pw_recorderRecordAction?._isCustomInterception);
      
      if (window.__pw_recorderRecordAction) {
        console.log('🧪 尝试调用测试...');
        try {
          window.__pw_recorderRecordAction({
            selector: 'button[data-testid="test-button"]',
            action: 'click',
            modifiers: [],
            button: 'left',
            clickCount: 1,
            position: { x: 100, y: 50 }
          });
          console.log('✅ 测试调用成功');
        } catch (error) {
          console.log('❌ 测试调用失败:', error.message);
        }
      } else {
        console.log('❌ __pw_recorderRecordAction 不存在，无法测试');
      }
    }

    // 提供测试方法到全局
    window.testRecordActionInterception = testRecordActionInterception;
  `;
}

module.exports = {
  getCode
}; 