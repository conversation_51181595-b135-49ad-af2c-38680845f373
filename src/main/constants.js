/**
 * 应用常量配置
 */

// 应用配置
const APP_CONFIG = {
  DEFAULT_LANGUAGE: 'javascript',
  DEFAULT_OUTPUT_FILE: './recorded-script.js',
  DEFAULT_WINDOW_TITLE: '🎬 Playwright 录制器',
  DEFAULT_CONTROL_PANEL_TITLE: '🎛️ 录制器控制面板',
  
  // 窗口配置
  CONTROL_PANEL_WIDTH: 450,
  CONTROL_PANEL_HEIGHT: 700,
  
  // 端口范围
  VITE_PORTS: [3000, 3001, 3002, 3003, 3004, 3005],
  
  // 文件监控间隔 (毫秒)
  FILE_MONITOR_INTERVAL: 200,
  
  // 浏览器启动参数
  BROWSER_ARGS: [
    '--no-first-run',
    '--no-default-browser-check',
    '--start-maximized',
    '--disable-web-security',
    '--disable-features=VizDisplayCompositor'
  ]
};

// 录制器模式
const RECORDER_MODES = {
  RECORDING: 'recording',
  INSPECT: 'inspect',
  PAUSED: 'none'
};



// IPC 事件名称
const IPC_EVENTS = {
  RECORDER_CONTROL: 'recorder-control',
  GET_RECORDER_STATUS: 'get-recorder-status',
  EXPORT_CODE: 'export-code',
  SELECT_ELEMENT: 'select-element',
  GET_ELEMENT_INFO: 'get-element-info',
  NAVIGATE_TO_URL: 'navigate-to-url',
  INJECT_VARIABLE: 'inject-variable',

  // JSON 回放功能
  REPLAY_JSON_DATA: 'replay-json-data',
  REPLAY_JSON_FILE: 'replay-json-file',
  REPLAY_SCRIPT_DATA: 'replay-script-data',
  STOP_REPLAY: 'stop-replay',

  // 渲染进程事件
  RECORDER_STATUS_UPDATE: 'recorder-status-update',
  CODE_UPDATED: 'code-updated',
  ELEMENT_SELECTED: 'element-selected',
  MODE_CHANGED: 'mode-changed',
  RECORDER_PAGE_MESSAGE: 'recorder-page-message',
  ACTION_RECORDED: 'action-recorded',
  PLAYWRIGHT_CODE_GENERATED: 'playwright-code-generated',
  REPLAY_STATUS_UPDATE: 'replay-status-update',
  NETWORK_REQUEST_CAPTURED: 'network-request-captured',
  RECORD_ACTION_INTERCEPTED: 'record-action-intercepted'
};

// 消息类型
const MESSAGE_TYPES = {
  ELEMENT_SELECTED: 'elementSelected',
  MODE_CHANGED: 'modeChanged',
  RECORDER_STATE_CHANGED: 'recorderStateChanged',
  USER_ACTION: 'userAction',
  PAGE_NAVIGATION: 'pageNavigation'
};



// 错误消息
const ERROR_MESSAGES = {
  ELECTRON_NOT_FOUND: '❌ 未找到 Electron，请先安装: npm install electron --save-dev',
  VITE_CONNECTION_FAILED: '❌ 无法连接到 Vite 开发服务器，请确保运行 npm run start:renderer',
  SYSTEM_START_FAILED: '❌ 系统启动失败',
  COMMUNICATION_FAILED: '❌ 建立通信连接失败',
  RECORDER_CONTROL_FAILED: '❌ 录制器控制失败'
};

// 成功消息
const SUCCESS_MESSAGES = {
  VITE_CONNECTED: '✅ 成功连接到 Vite 开发服务器',
  CONTROL_PANEL_CREATED: '🎛️ Electron 控制面板已创建',
  IPC_SETUP: '📡 IPC 通信已设置',
  PLAYWRIGHT_INITIALIZED: '🎬 Playwright 录制器已初始化',
  COMMUNICATION_ESTABLISHED: '📡 双向通信连接已建立',
  FILE_MONITOR_STARTED: '👁️ 文件监控已启动',
  SYSTEM_STARTED: '✅ 系统启动完成',
  CUSTOM_ICON_SET: 'Custom browser icon set successfully'
};

module.exports = {
  APP_CONFIG,
  RECORDER_MODES,
  IPC_EVENTS,
  MESSAGE_TYPES,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES
};
