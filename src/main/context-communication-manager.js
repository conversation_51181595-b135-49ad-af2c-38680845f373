/**
 * 上下文通信管理器 - 在 Context 级别处理所有页面与 Electron 的通信
 * 替代之前为每个页面单独创建管理器的模式
 */

class ContextCommunicationManager {
  constructor(context, messageHandler) {
    this.context = context;
    this.messageHandler = messageHandler;
  }

  /**
   * 初始化上下文级别的通信
   */
  async initialize() {
    try {
      // 在 context 级别暴露通信函数 - 所有页面都可以访问
      await this._exposeContextCommunicationFunctions();

      // 在 context 级别注入增强脚本 - 所有页面都会执行
      await this._injectContextEnhancedScript();

      console.log('📡 上下文通信管理器已初始化 - 适用于所有页面');
    } catch (error) {
      console.error('❌ 上下文通信初始化失败:', error);
      throw error;
    }
  }

  /**
   * 销毁连接
   */
  async destroy() {
    // Context 通信不需要特殊清理，context 关闭时会自动清理
    console.log('🔌 上下文通信管理器已销毁');
  }

  /**
   * 在 context 级别暴露通信函数给所有页面
   * @private
   */
  async _exposeContextCommunicationFunctions() {
    // 暴露给所有页面的通信函数
    await this.context.exposeFunction('sendToElectron', (data) => {
      this._handlePageCallback(data);
    });
    
    console.log('🌐 通信函数已暴露到 context - 所有页面可用');
  }

  /**
   * 在 context 级别注入增强脚本到所有页面
   * @private
   */
  async _injectContextEnhancedScript() {
    const { generateClientScript } = require('./client-script');
    const clientScript = generateClientScript();

    // 使用 context.addInitScript 确保所有页面（包括新 tab）都执行脚本
    await this.context.addInitScript(clientScript);
    console.log('🌉 增强录制器脚本已注入到 context - 适用于所有页面');
  }

  /**
   * 处理页面回调
   * @private
   */
  _handlePageCallback(data) {
    try {
      console.log('📨 收到页面消息:', data.type, data.data);

      if (this.messageHandler) {
        this.messageHandler(data);
      }
    } catch (error) {
      console.error('❌ 处理页面回调失败:', error);
    }
  }
}

module.exports = ContextCommunicationManager; 