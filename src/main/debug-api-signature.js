/**
 * 调试 API 签名 - 尝试不同的参数格式
 */

const { EnhancedOfficialExecutor } = require('./enhanced-official-executor');
const { mainFrameForAction } = require('../../node_modules/playwright-core/lib/server/recorder/recorderUtils.js');
const { serverSideCallMetadata } = require('playwright-core/lib/server');

async function debugApiSignature() {
  console.log('🔍 开始调试 API 签名...');
  
  const executor = new EnhancedOfficialExecutor({
    enableDiagnostics: true,
    enableStabilityEnhancements: false
  });
  
  try {
    // 初始化
    await executor.initialize({
      browserName: 'chromium',
      launchOptions: {
        headless: false,
        devtools: true
      },
      contextOptions: {
        viewport: { width: 1280, height: 720 }
      }
    });
    
    console.log('✅ 初始化完成');
    
    // 获取页面和pageAliases
    const page = executor.getCurrentPage();
    const pageAliases = executor.pageAliases;
    
    const actionInContext = {
      frame: {
        pageAlias: 'page',
        framePath: [],
        isMainFrame: true
      },
      action: {
        name: 'navigate',
        url: 'https://www.baidu.com',
        signals: []
      },
      startTime: Date.now()
    };
    
    const mainFrame = mainFrameForAction(pageAliases, actionInContext);
    const callMetadata = serverSideCallMetadata();
    const url = 'https://www.baidu.com';
    
    console.log('📊 准备测试不同的 API 签名...');
    
    // 测试 1: 原始方式
    console.log('\n🔍 测试 1: mainFrame.goto(callMetadata, url, options)');
    try {
      await mainFrame.goto(callMetadata, url, { timeout: 5000 });
      console.log('✅ 测试 1 成功');
    } catch (error) {
      console.error('❌ 测试 1 失败:', error.message);
    }
    
    // 测试 2: 不使用 callMetadata
    console.log('\n🔍 测试 2: mainFrame.goto(url, options)');
    try {
      await mainFrame.goto(url, { timeout: 5000 });
      console.log('✅ 测试 2 成功');
    } catch (error) {
      console.error('❌ 测试 2 失败:', error.message);
    }
    
    // 测试 3: 只传 url
    console.log('\n🔍 测试 3: mainFrame.goto(url)');
    try {
      await mainFrame.goto(url);
      console.log('✅ 测试 3 成功');
    } catch (error) {
      console.error('❌ 测试 3 失败:', error.message);
    }
    
    // 测试 4: 使用 _page.goto
    console.log('\n🔍 测试 4: mainFrame._page.goto(url, options)');
    try {
      await mainFrame._page.goto(url, { timeout: 5000 });
      console.log('✅ 测试 4 成功');
    } catch (error) {
      console.error('❌ 测试 4 失败:', error.message);
    }
    
    // 测试 5: 检查 mainFrame 的实际方法
    console.log('\n🔍 测试 5: 检查 mainFrame 的实际方法');
    console.log('- mainFrame keys:', Object.keys(mainFrame).slice(0, 10));
    console.log('- mainFrame.goto 类型:', typeof mainFrame.goto);
    console.log('- mainFrame.goto length:', mainFrame.goto.length);
    
    // 检查是否有其他的 navigate 方法
    const navigateMethods = Object.keys(mainFrame).filter(key => 
      key.includes('goto') || key.includes('navigate') || key.includes('page')
    );
    console.log('- navigate 相关方法:', navigateMethods);
    
    // 测试 6: 使用 evaluate 获取更多信息
    console.log('\n🔍 测试 6: 查看 mainFrame 的构造信息');
    console.log('- mainFrame.constructor.name:', mainFrame.constructor.name);
    console.log('- mainFrame._page 类型:', typeof mainFrame._page);
    
    // 等待用户观察结果
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 如果之前的测试都失败了，我们直接用 page.goto 作为对比
    console.log('\n🔍 对比测试: 使用 page.goto');
    try {
      await page.goto('https://www.example.com', { timeout: 5000 });
      console.log('✅ page.goto 成功');
    } catch (error) {
      console.error('❌ page.goto 失败:', error.message);
    }
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
  } catch (error) {
    console.error('❌ 调试失败:', error);
  } finally {
    await executor.close();
  }
}

if (require.main === module) {
  debugApiSignature().catch(console.error);
}

module.exports = { debugApiSignature }; 