/**
 * IPC 管理器 - 处理主进程和渲染进程之间的通信
 * 使用全局 Playwright 回放 API 提供更好的兼容性
 */

const { ipcMain } = require('electron');
const { IPC_EVENTS } = require('./constants');

// 加载 playwright-core 以触发全局 API patch
require('playwright-core');

class IPCManager {
  constructor(recorder) {
    this.recorder = recorder;
    this.handlers = new Map();

    // 检查全局 Playwright 回放 API 是否可用
    this.globalAPI = global.playwrightReplayAPI;
    if (this.globalAPI) {
      console.log('✅ IPC 管理器: 全局 Playwright 回放 API 已加载');
      console.log('📊 API 版本:', this.globalAPI.version);
    } else {
      console.warn('⚠️ IPC 管理器: 全局 Playwright 回放 API 不可用');
    }
  }

  /**
   * 设置所有 IPC 处理器
   */
  setupHandlers() {
    this._registerHandler(IPC_EVENTS.RECORDER_CONTROL, this._handleRecorderControl.bind(this));
    this._registerHandler(IPC_EVENTS.GET_RECORDER_STATUS, this._handleGetRecorderStatus.bind(this));
    this._registerHandler(IPC_EVENTS.EXPORT_CODE, this._handleExportCode.bind(this));
    this._registerHandler(IPC_EVENTS.SELECT_ELEMENT, this._handleSelectElement.bind(this));
    this._registerHandler(IPC_EVENTS.GET_ELEMENT_INFO, this._handleGetElementInfo.bind(this));
    this._registerHandler(IPC_EVENTS.NAVIGATE_TO_URL, this._handleNavigateToUrl.bind(this));
    this._registerHandler(IPC_EVENTS.INJECT_VARIABLE, this._handleInjectVariable.bind(this));

    // JSON 回放处理器
    this._registerHandler(IPC_EVENTS.REPLAY_JSON_DATA, this._handleReplayJsonData.bind(this));
    this._registerHandler(IPC_EVENTS.REPLAY_JSON_FILE, this._handleReplayJsonFile.bind(this));
    this._registerHandler(IPC_EVENTS.REPLAY_SCRIPT_DATA, this._handleReplayScriptData.bind(this));
    this._registerHandler(IPC_EVENTS.STOP_REPLAY, this._handleStopReplay.bind(this));

    // 设置 Playwright 代码生成事件监听器（直接来自 Playwright 补丁）
    this._setupPlaywrightCodeGeneratedListener();

    console.log('📡 IPC 处理器已设置');
  }

  /**
   * 使用全局 API 执行动作（如果可用）
   * @param {Object} actionData - 动作数据
   * @param {Map} pageAliases - 页面别名映射
   * @returns {Promise<boolean>} - 是否成功使用全局 API 执行
   */
  async executeActionWithGlobalAPI(actionData, pageAliases) {
    if (!this.globalAPI) {
      return false;
    }

    try {
      // 使用全局 API 创建标准的 ActionInContext 对象
      const actionInContext = this.globalAPI.createActionInContext(
        actionData.pageAlias || 'page',
        actionData.framePath || [],
        {
          name: actionData.name,
          selector: actionData.selector,
          url: actionData.url,
          text: actionData.text,
          button: actionData.button || 'left',
          modifiers: actionData.modifiers || 0,
          clickCount: actionData.clickCount || 1,
          key: actionData.key,
          options: actionData.options,
          files: actionData.files,
          position: actionData.position
        }
      );

      console.log(`🌍 IPC: 使用全局 API 执行动作: ${actionData.name}`);

      // 使用官方的 performAction 函数
      await this.globalAPI.performAction(pageAliases, actionInContext);

      console.log(`✅ IPC: 全局 API 执行成功: ${actionData.name}`);
      return true;

    } catch (error) {
      console.log(`⚠️ IPC: 全局 API 执行失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 清理所有 IPC 处理器
   */
  cleanup() {
    this.handlers.forEach((handler, event) => {
      ipcMain.removeHandler(event);
    });
    this.handlers.clear();
    console.log('🧹 IPC 处理器已清理');
  }

  /**
   * 注册 IPC 处理器
   * @private
   */
  _registerHandler(event, handler) {
    ipcMain.handle(event, handler);
    this.handlers.set(event, handler);
  }

  /**
   * 设置 Playwright 代码生成事件监听器
   * @private
   */
  _setupPlaywrightCodeGeneratedListener() {
    // 监听来自 Playwright 补丁的直接 IPC 事件
    ipcMain.on('playwright-code-generated', (event, data) => {
      console.log('🎭 收到 Playwright 官方代码生成数据 (直接 IPC):', {
        sourcesCount: data.sources?.length || 0,
        mode: data.mode,
        primaryPageURL: data.primaryPageURL,
        timestamp: data.timestamp
      });

      // 转发给消息处理器
      if (this.recorder.messageHandler) {
        this.recorder.messageHandler._handlePlaywrightCodeGenerated(data);
      }
    });
  }

  /**
   * 处理录制器控制
   * @private
   */
  async _handleRecorderControl(event, action, data) {
    try {
      return await this.recorder.handleRecorderControl(action, data);
    } catch (error) {
      console.error(`❌ 录制器控制失败 (${action}):`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 处理获取录制器状态
   * @private
   */
  async _handleGetRecorderStatus() {
    try {
      return {
        isRecording: this.recorder.isRecording,
        generatedCode: this.recorder.generatedCode,
        codeLength: this.recorder.generatedCode.length,
        selectedElement: this.recorder.selectedElement
      };
    } catch (error) {
      console.error('❌ 获取录制器状态失败:', error);
      return { error: error.message };
    }
  }

  /**
   * 处理导出代码
   * @private
   */
  async _handleExportCode() {
    try {
      return this.recorder.generatedCode;
    } catch (error) {
      console.error('❌ 导出代码失败:', error);
      return '';
    }
  }

  /**
   * 处理选择元素
   * @private
   */
  async _handleSelectElement() {
    try {
      return await this.recorder.handleRecorderControl('selectElement', {});
    } catch (error) {
      console.error('❌ 选择元素失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 处理获取元素信息
   * @private
   */
  async _handleGetElementInfo(event, selector) {
    try {
      return await this.recorder.handleRecorderControl('getElementInfo', { selector });
    } catch (error) {
      console.error('❌ 获取元素信息失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 处理导航到 URL
   * @private
   */
  async _handleNavigateToUrl(event, url) {
    try {
      return await this.recorder.handleRecorderControl('navigate', { url });
    } catch (error) {
      console.error('❌ 导航失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 处理变量注入
   * @private
   */
  async _handleInjectVariable(event, { name, value, selector }) {
    try {
      if (this.recorder.page) {
        await this.recorder.page.fill(selector, value);

        const action = {
          type: 'variable_injection',
          name,
          value,
          selector,
          timestamp: Date.now()
        };

        this.recorder.sendToRenderer(IPC_EVENTS.ACTION_RECORDED, action);
      }
      return { success: true };
    } catch (error) {
      console.error('❌ 变量注入失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 处理 JSON 数据回放
   * @private
   */
  async _handleReplayJsonData(event, jsonData) {
    try {
      console.log('🎬 开始回放 JSON 数据...');

      // 初始化 JSON 回放执行器
      if (!this.recorder.jsonReplayExecutor) {
        const { ReliableJsonReplayExecutor } = require('./reliable-json-replay-executor');
        this.recorder.jsonReplayExecutor = new ReliableJsonReplayExecutor();
      }

      // 发送回放开始状态
      this.recorder.sendToRenderer(IPC_EVENTS.REPLAY_STATUS_UPDATE, {
        status: 'starting',
        message: '正在初始化回放...',
        timestamp: Date.now()
      });

      // 执行 JSON 数据回放
      await this.recorder.jsonReplayExecutor.executeJsonString(jsonData);

      // 发送回放完成状态
      this.recorder.sendToRenderer(IPC_EVENTS.REPLAY_STATUS_UPDATE, {
        status: 'completed',
        message: '回放完成！',
        timestamp: Date.now()
      });

      return { success: true, message: '回放完成' };
    } catch (error) {
      console.error('❌ JSON 数据回放失败:', error);

      // 发送回放失败状态
      this.recorder.sendToRenderer(IPC_EVENTS.REPLAY_STATUS_UPDATE, {
        status: 'error',
        message: `回放失败: ${error.message}`,
        error: error.message,
        timestamp: Date.now()
      });

      return { success: false, error: error.message };
    }
  }

  /**
   * 处理 JSON 文件回放
   * @private
   */
  async _handleReplayJsonFile(event, filePath) {
    try {
      console.log('🎬 开始回放 JSON 文件:', filePath);

      // 初始化 JSON 回放执行器
      if (!this.recorder.jsonReplayExecutor) {
        const { ReliableJsonReplayExecutor } = require('./reliable-json-replay-executor');
        this.recorder.jsonReplayExecutor = new ReliableJsonReplayExecutor();
      }

      // 发送回放开始状态
      this.recorder.sendToRenderer(IPC_EVENTS.REPLAY_STATUS_UPDATE, {
        status: 'starting',
        message: `正在回放文件: ${filePath}`,
        timestamp: Date.now()
      });

      // 执行 JSON 文件回放
      await this.recorder.jsonReplayExecutor.executeJsonFile(filePath);

      // 发送回放完成状态
      this.recorder.sendToRenderer(IPC_EVENTS.REPLAY_STATUS_UPDATE, {
        status: 'completed',
        message: '文件回放完成！',
        timestamp: Date.now()
      });

      return { success: true, message: '文件回放完成' };
    } catch (error) {
      console.error('❌ JSON 文件回放失败:', error);

      // 发送回放失败状态
      this.recorder.sendToRenderer(IPC_EVENTS.REPLAY_STATUS_UPDATE, {
        status: 'error',
        message: `文件回放失败: ${error.message}`,
        error: error.message,
        timestamp: Date.now()
      });

      return { success: false, error: error.message };
    }
  }

  /**
   * 处理脚本数据回放
   * @private
   */
  async _handleReplayScriptData(event, scriptData) {
    try {
      console.log('🎯 开始回放脚本数据...', {
        name: scriptData.name,
        type: scriptData.type,
        executionMode: scriptData.executionMode,
        checkpointCount: scriptData.checkpoint?.length || 0,
        executionStepsCount: scriptData.executionSteps?.length || 0,
        outputVariablesCount: scriptData.outputVariables?.length || 0
      });

      // 初始化脚本回放执行器
      if (!this.recorder.scriptReplayExecutor) {
        const { ScriptReplayExecutor } = require('./script-replay-executor');
        this.recorder.scriptReplayExecutor = new ScriptReplayExecutor();
      }

      // 初始化执行器（如果还没有初始化）
      if (!this.recorder.scriptReplayExecutor.browser) {
        console.log('🚀 初始化脚本回放执行器...');
        await this.recorder.scriptReplayExecutor.initialize();
      }

      // 发送回放开始状态
      this.recorder.sendToRenderer(IPC_EVENTS.REPLAY_STATUS_UPDATE, {
        status: 'starting',
        message: '正在初始化脚本回放...',
        timestamp: Date.now(),
        pagesInfo: this.recorder.scriptReplayExecutor.getAllPagesInfo()
      });

      // 根据执行模式执行脚本
      const result = await this.recorder.scriptReplayExecutor.executeScript(scriptData);

      // 发送详细的回放完成状态
      this.recorder.sendToRenderer(IPC_EVENTS.REPLAY_STATUS_UPDATE, {
        status: result.success ? 'completed' : 'error',
        message: result.success ? '脚本回放完成！' : result.error,
        timestamp: Date.now(),
        result: result,
        pagesInfo: this.recorder.scriptReplayExecutor.getAllPagesInfo()
      });

      return result;
    } catch (error) {
      console.error('❌ 脚本数据回放失败:', error);

      // 发送回放失败状态
      this.recorder.sendToRenderer(IPC_EVENTS.REPLAY_STATUS_UPDATE, {
        status: 'error',
        message: `脚本回放失败: ${error.message}`,
        error: error.message,
        timestamp: Date.now(),
        result: { success: false, error: error.message }
      });

      return { success: false, error: error.message };
    }
  }

  /**
   * 处理停止回放
   * @private
   */
  async _handleStopReplay(event) {
    try {
      console.log('⏹️ 停止回放...');

      let closedPages = [];

      if (this.recorder.jsonReplayExecutor) {
        closedPages = this.recorder.jsonReplayExecutor.getAllPagesInfo();
        await this.recorder.jsonReplayExecutor.close();
        this.recorder.jsonReplayExecutor = null;
      }

      if (this.recorder.scriptReplayExecutor) {
        // 停止监听模式
        if (this.recorder.scriptReplayExecutor.monitoringActive) {
          this.recorder.scriptReplayExecutor.stopMonitoring();
        }
        
        closedPages = this.recorder.scriptReplayExecutor.getAllPagesInfo();
        await this.recorder.scriptReplayExecutor.close();
        this.recorder.scriptReplayExecutor = null;
      }

      // 发送回放停止状态
      this.recorder.sendToRenderer(IPC_EVENTS.REPLAY_STATUS_UPDATE, {
        status: 'stopped',
        message: '回放已停止',
        timestamp: Date.now(),
        closedPages: closedPages
      });

      return { success: true, message: '回放已停止' };
    } catch (error) {
      console.error('❌ 停止回放失败:', error);
      return { success: false, error: error.message };
    }
  }
}

module.exports = IPCManager;
