/**
 * 消息处理器 - 处理来自录制页面的各种消息
 */

const { MESSAGE_TYPES, IPC_EVENTS } = require('./constants');

class MessageHandler {
  constructor(recorder) {
    this.recorder = recorder;
  }

  /**
   * 处理页面消息
   * @param {Object} data - 消息数据
   */
  handlePageMessage(data) {
    try {
      console.log('📨 收到页面消息:', data.type, data.data);

      const handlers = {
        [MESSAGE_TYPES.ELEMENT_SELECTED]: this._handleElementSelected.bind(this),
        [MESSAGE_TYPES.RECORDER_STATE_CHANGED]: this._handleRecorderStateChanged.bind(this),
        'playwrightCodeGenerated': this._handlePlaywrightCodeGenerated.bind(this),
        'recordActionIntercepted': this._handleRecordActionIntercepted.bind(this)
      };

      const handler = handlers[data.type];
      if (handler) {
        handler(data.data);
      } else {
        console.log(`📨 未处理的消息类型: ${data.type}`);
      }

      // 转发消息到控制面板
      this._forwardToControlPanel(data);

    } catch (error) {
      console.error('❌ 处理页面消息失败:', error);
    }
  }

  /**
   * 处理元素选择
   * @private
   */
  _handleElementSelected(data) {
    const { elementInfo, event } = data;
    this.recorder.selectedElement = elementInfo;

    this._logElementInfo(elementInfo);

    // 注意：选择器现在在客户端脚本中生成，这里直接使用传入的选择器
    // 不再在主进程中生成选择器，因为主进程无法访问页面DOM上下文
    const selectors = elementInfo.selectors || {
      playwright: elementInfo.selector,
      css: elementInfo.selector,
      xpath: null,
      role: null,
      text: null
    };

    // 发送详细信息到控制面板
    this.recorder.sendToRenderer(IPC_EVENTS.ELEMENT_SELECTED, {
      elementInfo,
      event,
      selectors,
      timestamp: Date.now()
    });

    // 保持在选择模式（如果是持续选择模式）
    // this._maintainInspectionMode();
  }

  /**
   * 记录元素信息
   * @private
   */
  _logElementInfo(elementInfo) {
    console.log('🎯 元素已选中:');
    console.log(`   标签: ${elementInfo.tagName}`);
    console.log(`   Playwright 选择器: ${elementInfo.selectors?.playwright || '未生成'}`);
    console.log(`   CSS 选择器: ${elementInfo.selectors?.css || '未生成'}`);
    console.log(`   XPath 选择器: ${elementInfo.selectors?.xpath || '未生成'}`);
    console.log(`   文本: ${elementInfo.textContent || elementInfo.innerText || '(无文本)'}`);
  }

  /**
   * 处理录制器状态变化
   * @private
   */
  _handleRecorderStateChanged(data) {
    console.log('🎬 录制器状态变化:', {
      mode: data.mode,
      isRecording: data.isRecording,
      isInspecting: data.isInspecting,
      previousMode: data.previousState?.mode,
      currentMode: data.currentState?.mode
    });

    // 更新录制器状态
    this.recorder.isRecording = data.isRecording;
    this.recorder.mode = data.mode;
    this.recorder.isInspecting = data.isInspecting;

    // 记录详细的状态变化信息
    if (data.previousState && data.currentState) {
      console.log('📊 详细状态变化:');
      console.log(`   模式变化: ${data.previousState.mode} → ${data.currentState.mode}`);
      console.log(`   录制状态: ${data.isRecording ? '🔴 录制中' : '⏸️ 已暂停'}`);
      console.log(`   检查状态: ${data.isInspecting ? '🔍 检查中' : '👁️ 非检查'}`);

      if (data.currentState.actionSelector) {
        console.log(`   当前选择器: ${data.currentState.actionSelector}`);
      }

      if (data.currentState.language) {
        console.log(`   当前语言: ${data.currentState.language}`);
      }
    }

    // 发送状态更新到控制面板
    this.recorder.sendToRenderer(IPC_EVENTS.RECORDER_STATUS_UPDATE, {
      isRecording: data.isRecording,
      mode: data.mode,
      isInspecting: data.isInspecting,
      state: data.currentState,
      timestamp: data.timestamp
    });
  }

  /**
   * 转发消息到控制面板
   * @private
   */
  _forwardToControlPanel(data) {
    this.recorder.sendToRenderer(IPC_EVENTS.RECORDER_PAGE_MESSAGE, data);
  }


  /**
   * 保持检查模式
   * @private
   */
  _maintainInspectionMode() {
    if (this.recorder.page) {
      this.recorder.page.evaluate(() => {
        if (window.electronRecorderBridge) {
          window.electronRecorderBridge.keepInspecting();
        }
      }).catch(console.error);
    }
  }

  /**
   * 处理录制动作拦截
   * @private
   */
  _handleRecordActionIntercepted(data) {
    console.log('🎬 收到录制动作拦截数据:', {
      actionType: data.actionInfo?.action || 'unknown',
      selector: data.selector,
      hasElement: !!data.element,
      hasDetailedInfo: !!data.detailedInfo,
      hasError: !!data.error,
      timestamp: data.timestamp
    });

    // 如果有错误，记录错误信息
    if (data.error) {
      console.error('❌ 录制动作拦截错误:', data.error);
    }

    // 记录动作信息
    if (data.actionInfo) {
      console.log('📝 动作信息:', {
        action: data.actionInfo.action,
        selector: data.actionInfo.selector,
        modifiers: data.actionInfo.modifiers,
        button: data.actionInfo.button,
        clickCount: data.actionInfo.clickCount,
        position: data.actionInfo.position
      });
    }

    // 记录元素信息
    if (data.element) {
      console.log('🎯 基本元素信息:', {
        tagName: data.element.tagName,
        id: data.element.id,
        className: data.element.className,
        textContent: data.element.textContent?.substring(0, 50) + (data.element.textContent?.length > 50 ? '...' : ''),
        value: data.element.value,
        href: data.element.href
      });
    }

    // 记录详细元素信息
    if (data.detailedInfo) {
      console.log('📊 详细元素信息:', {
        tagName: data.detailedInfo.tagName,
        selectors: data.detailedInfo.selectors,
        position: data.detailedInfo.position,
        styles: data.detailedInfo.styles,
        attributes: data.detailedInfo.attributes,
        parentInfo: data.detailedInfo.parentInfo,
        childrenCount: data.detailedInfo.childrenCount
      });
    }

    // 发送到渲染进程
    this.recorder.sendToRenderer(IPC_EVENTS.RECORD_ACTION_INTERCEPTED, {
      actionInfo: data.actionInfo,
      selector: data.selector,
      element: data.element,
      detailedInfo: data.detailedInfo,
      error: data.error,
      timestamp: data.timestamp || Date.now()
    });
  }

  /**
   * 处理 Playwright 代码生成
   * @private
   */
  _handlePlaywrightCodeGenerated(data) {
    console.log('🎭 收到 Playwright 官方代码生成数据:', {
      sourcesCount: data.sources?.length || 0,
      mode: data.mode,
      primaryPageURL: data.primaryPageURL,
      timestamp: data.timestamp
    });

    // 更新录制器的生成代码
    if (data.sources && data.sources.length > 0) {
      // 获取主要的 JavaScript 代码
      const jsSource = data.sources.find(source =>
        source.language === 'javascript' ||
        source.id === 'javascript' ||
        source.label?.toLowerCase().includes('javascript')
      );

      // 获取 JSON 格式的数据
      const jsonSource = data.sources.find(source =>
        source.language === 'javascript' && source.id === 'jsonl' ||
        source.label?.toLowerCase().includes('jsonl') ||
        source.label?.toLowerCase().includes('json')
      );

      if (jsSource) {
        this.recorder.generatedCode = jsSource.text;
        console.log('📝 更新生成的代码 (长度:', jsSource.text.length, ')');
      }

      // 存储JSON数据用于回放
      if (jsonSource) {
        this.recorder.jsonData = jsonSource.text;
        console.log('📊 更新JSON数据 (长度:', jsonSource.text.length, ')');
      }
    }

    // 发送到渲染进程
    this.recorder.sendToRenderer(IPC_EVENTS.PLAYWRIGHT_CODE_GENERATED, {
      sources: data.sources,
      primaryPageURL: data.primaryPageURL,
      mode: data.mode,
      timestamp: data.timestamp,
      recorderSources: data.recorderSources,
      userSources: data.userSources
    });

    // 同时发送代码更新事件（保持兼容性）
    if (this.recorder.generatedCode) {
      this.recorder.sendToRenderer(IPC_EVENTS.CODE_UPDATED, {
        code: this.recorder.generatedCode,
        length: this.recorder.generatedCode.length,
        timestamp: data.timestamp,
        source: 'playwright-official'
      });
    }
  }
}

module.exports = MessageHandler;
