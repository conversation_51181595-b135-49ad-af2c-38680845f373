/**
 * 简化版网络监听器 - 在 Context 级别监听所有页面的网络请求
 */

class NetworkMonitor {
  constructor(recorder) {
    this.recorder = recorder;
    this.requestCounter = 0;
    this.pendingRequests = new Map(); // 存储待完成的请求
    this.isContextListening = false; // 标记是否已设置 Context 级别监听
  }

  /**
   * 设置 Context 级别的网络监听 - 自动覆盖所有页面
   * @param {BrowserContext} context - 浏览器上下文
   */
  async setupContextNetworkMonitoring(context) {
    if (!context) {
      console.warn('⚠️ Context 未初始化，无法设置网络监听');
      return;
    }

    // 避免重复设置
    if (this.isContextListening) {
      console.log('🌐 Context 网络监听已存在，跳过设置');
      return;
    }

    // 在 Context 级别设置网络监听 - 自动覆盖所有页面
    context.on('request', (request) => this._handleRequest(request));
    context.on('response', (response) => this._handleResponse(response));
    context.on('requestfailed', (request) => this._handleRequestFailed(request));

    this.isContextListening = true;
    console.log('🌐 Context 网络监听已启用 - 覆盖所有页面（包括新标签页）');
  }

  /**
   * 设置网络监听 - 兼容旧接口，使用 Context 级别监听
   */
  async setupNetworkMonitoring() {
    if (!this.recorder.context) {
      console.warn('⚠️ Context 未初始化，无法设置网络监听');
      return;
    }

    await this.setupContextNetworkMonitoring(this.recorder.context);
  }

  /**
   * 处理请求开始
   */
  _handleRequest(request) {
    // 只监听API相关的请求
    if (!this._shouldMonitorRequest(request)) {
      return;
    }

    const requestId = ++this.requestCounter;
    const timestamp = Date.now();
    
    const requestInfo = {
      id: requestId,
      method: request.method(),
      url: request.url(),
      timestamp: timestamp,
      requestHeaders: request.headers(),
      requestBody: this._getRequestBody(request),
      startTime: timestamp,
      resourceType: request.resourceType() // 资源类型
    };

    // 使用URL+时间戳作为唯一标识
    const requestKey = `${request.url()}_${timestamp}`;
    this.pendingRequests.set(requestKey, {
      ...requestInfo,
      request: request
    });
    
    console.log(`📤 ${request.method()} ${request.url()} [${request.resourceType()}]`);
  }

  /**
   * 处理响应完成
   */
  async _handleResponse(response) {
    const request = response.request();
    
    // 在响应阶段也应用过滤逻辑
    if (!this._shouldMonitorRequest(request)) {
      return;
    }
    
    // 查找对应的请求
    let requestInfo = null;
    let requestKey = null;
    
    for (const [key, info] of this.pendingRequests.entries()) {
      if (info.request === request) {
        requestInfo = info;
        requestKey = key;
        break;
      }
    }

    if (!requestInfo) {
      // 如果没找到对应请求，不再创建临时信息，直接返回
      return;
    }

    // 从待处理列表中移除
    this.pendingRequests.delete(requestKey);

    // 计算响应时间
    const responseTime = Date.now() - requestInfo.startTime;
    
    // 获取响应数据
    const responseData = await this._getResponseData(response);
    
    // 构建完整的请求信息
    const completeRequestInfo = {
      id: requestInfo.id,
      method: requestInfo.method,
      url: requestInfo.url,
      timestamp: requestInfo.timestamp,
      requestHeaders: requestInfo.requestHeaders,
      requestBody: requestInfo.requestBody,
      resourceType: requestInfo.resourceType,
      status: response.status(),
      statusText: response.statusText(),
      responseHeaders: response.headers(),
      responseBody: responseData.body,
      time: `${responseTime}ms`,
      size: responseData.size
    };

    // 发送到渲染进程
    this._sendToRenderer(completeRequestInfo);
    
    console.log(`📥 ${response.status()} ${request.url()} (${responseTime}ms)`);
  }

  /**
   * 处理请求失败
   */
  _handleRequestFailed(request) {
    // 在失败处理阶段也应用过滤逻辑
    if (!this._shouldMonitorRequest(request)) {
      return;
    }
    
    // 查找对应的请求
    let requestInfo = null;
    let requestKey = null;
    
    for (const [key, info] of this.pendingRequests.entries()) {
      if (info.request === request) {
        requestInfo = info;
        requestKey = key;
        break;
      }
    }

    if (requestInfo) {
      this.pendingRequests.delete(requestKey);
      
      const responseTime = Date.now() - requestInfo.startTime;
      
      const failedRequestInfo = {
        id: requestInfo.id,
        method: requestInfo.method,
        url: requestInfo.url,
        timestamp: requestInfo.timestamp,
        requestHeaders: requestInfo.requestHeaders,
        requestBody: requestInfo.requestBody,
        resourceType: requestInfo.resourceType,
        status: 0,
        statusText: 'Failed',
        responseHeaders: {},
        responseBody: null,
        time: `${responseTime}ms`,
        size: '0B'
      };

      this._sendToRenderer(failedRequestInfo);
      console.log(`❌ 请求失败: ${request.url()}`);
    } else {
      // 失败请求未找到对应信息，可能是被过滤的请求，忽略
    }
  }

  /**
   * 判断是否应该监听请求（只监听API接口和表单提交）
   */
  _shouldMonitorRequest(request) {
    const url = request.url();
    const method = request.method();
    const resourceType = request.resourceType();
    
    // 过滤掉明显的系统URL
    const systemUrls = [
      'data:',
      'chrome-extension:',
      'moz-extension:',
      'about:',
      'chrome:',
      'edge:',
      'devtools://'
    ];
    
    if (systemUrls.some(prefix => url.startsWith(prefix))) {
      return false;
    }
    
    // 明确过滤掉所有资源类型
    const resourceTypes = ['image', 'font', 'media', 'stylesheet', 'script', 'manifest', 'texttrack', 'eventsource', 'websocket'];
    if (resourceTypes.includes(resourceType)) {
      return false;
    }
    
    // 优先过滤静态资源文件扩展名（这个检查优先级最高）
    const staticExtensions = [
      '.html', '.htm', '.css', '.js', '.jsx', '.ts', '.tsx', '.vue', '.svelte',
      '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.webp', '.bmp',
      '.woff', '.woff2', '.ttf', '.eot', '.otf',
      '.mp4', '.mp3', '.wav', '.avi', '.mov',
      '.pdf', '.doc', '.docx', '.xls', '.xlsx',
      '.zip', '.rar', '.tar', '.gz'
    ];
    
    // 严格检查静态扩展名，使用更精确的正则匹配
    for (const ext of staticExtensions) {
      // 匹配文件扩展名，确保前面是非字母数字字符或开头，后面是参数、结尾或非字母数字字符
      const regex = new RegExp(`(^|[^a-zA-Z0-9])\\${ext}(\\?|#|$|[^a-zA-Z0-9])`, 'i');
      if (regex.test(url)) {
        return false;
      }
    }
    
    // 1. 非GET请求通常是API操作（POST、PUT、DELETE、PATCH等）
    if (method !== 'GET') {
      console.log(`✅ API请求: ${method} ${url}`);
      return true;
    }
    
    // 2. 检查Content-Type，表单提交和JSON数据
    const headers = request.headers();
    const contentType = headers['content-type'] || headers['Content-Type'] || '';
    if (contentType.includes('application/json') || 
        contentType.includes('application/x-www-form-urlencoded') ||
        contentType.includes('multipart/form-data')) {
      console.log(`✅ API请求: ${url} [${contentType}]`);
      return true;
    }
    
    // 3. 资源类型为 xhr 或 fetch 的，且URL包含明确的API模式
    if (resourceType === 'xhr' || resourceType === 'fetch') {
      const apiPatterns = [
        '/api/',
        '/apis/',
        '/v1/',
        '/v2/',
        '/v3/',
        '/v4/',
        '/graphql',
        '/rest/',
        '/service/',
        '/services/',
        '/gateway/',
        '/backend/',
        '/server/',
        '/rpc/',
        '/ajax/',
        '/data/',
        '/query/',
        '/mutation/'
      ];
      
      if (apiPatterns.some(pattern => url.includes(pattern))) {
        console.log(`✅ API请求: ${url}`);
        return true;
      }
      
      // 检查域名中的api模式（更严格）
      if (url.match(/^https?:\/\/[^\/]*api[^\/]*\//i)) {
        console.log(`✅ API请求: ${url}`);
        return true;
      }
      
      return false;
    }
    
    // 4. 对于document类型，只监听看起来像API端点的URL
    if (resourceType === 'document') {
      const apiPatterns = [
        '/api/',
        '/apis/',
        '/v1/',
        '/v2/',
        '/v3/',
        '/v4/',
        '/graphql',
        '/rest/',
        '/service/',
        '/services/',
        '/gateway/',
        '/backend/',
        '/server/',
        '/rpc/',
        '/ajax/',
        '/data/',
        '/query/',
        '/mutation/'
      ];
      
      // 只有明确包含API模式的document请求才监听
      if (apiPatterns.some(pattern => url.includes(pattern))) {
        console.log(`✅ API请求: ${url}`);
        return true;
      }
      
      // 检查URL是否以.json结尾（可能是API数据）
      if (url.toLowerCase().match(/\.json(\?|#|$)/)) {
        console.log(`✅ API请求: ${url}`);
        return true;
      }
      
      return false;
    }
    
    // 5. 其他类型的请求，只有明确的API模式才监听
    const strictApiPatterns = [
      '/api/',
      '/apis/',
      '/graphql',
      '/rest/',
      '/rpc/'
    ];
    
    if (strictApiPatterns.some(pattern => url.includes(pattern))) {
      console.log(`✅ API请求: ${url}`);
      return true;
    }
    
    // 检查域名中的api模式（更严格）
    if (url.match(/^https?:\/\/[^\/]*api[^\/]*\//i)) {
      console.log(`✅ API请求: ${url}`);
      return true;
    }
    
    // 默认不监听其他请求
    return false;
  }

  /**
   * 获取请求体
   */
  _getRequestBody(request) {
    try {
      const postData = request.postData();
      if (postData) {
        try {
          return JSON.parse(postData);
        } catch {
          return postData;
        }
      }
    } catch (error) {
      // 忽略错误
    }
    return null;
  }

  /**
   * 获取响应数据
   */
  async _getResponseData(response) {
    try {
      const responseText = await response.text();
      let responseBody;
      
      // 尝试解析JSON
      const contentType = response.headers()['content-type'] || '';
      if (contentType.includes('application/json') || contentType.includes('text/json')) {
        try {
          responseBody = JSON.parse(responseText);
        } catch {
          responseBody = responseText;
        }
      } else {
        // 对于非JSON响应，如果内容太长就截断
        if (responseText.length > 1000) {
          responseBody = responseText.substring(0, 1000) + '... [截断]';
        } else {
          responseBody = responseText;
        }
      }
      
      return {
        body: responseBody,
        size: this._formatSize(responseText.length)
      };
    } catch (error) {
      console.warn('⚠️ 无法获取响应数据:', error.message);
      return {
        body: null,
        size: '0B'
      };
    }
  }

  /**
   * 发送数据到渲染进程
   */
  _sendToRenderer(requestInfo) {
    if (this.recorder.sendToRenderer) {
      const { IPC_EVENTS } = require('./constants');
      this.recorder.sendToRenderer(IPC_EVENTS.NETWORK_REQUEST_CAPTURED, requestInfo);
    }
  }

  /**
   * 格式化文件大小
   */
  _formatSize(bytes) {
    if (bytes === 0) return '0B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + sizes[i];
  }

  /**
   * 清理资源
   */
  cleanup() {
    // Context 级别的监听不需要手动清理单个页面的监听器
    // 但需要重置监听状态，以便下次重新初始化
    this.isContextListening = false;
    this.pendingRequests.clear();
    console.log('🌐 网络监听已清理');
  }
}

module.exports = NetworkMonitor; 