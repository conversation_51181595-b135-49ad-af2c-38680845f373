/**
 * Playwright 管理器 - 管理 Playwright 浏览器和录制器
 */

const { chromium } = require('playwright');
const path = require('path');
const { APP_CONFIG, SUCCESS_MESSAGES } = require('./constants');
const NetworkMonitor = require('./network-monitor');
const ContextCommunicationManager = require('./context-communication-manager');

class PlaywrightManager {
  constructor(recorder) {
    this.recorder = recorder;
    this.networkMonitor = new NetworkMonitor(recorder);
    
    // 使用 context 级别的通信管理器
    this.contextCommunicationManager = null;
  }

  /**
   * 初始化 Playwright 录制器
   */
  async initialize() {
    await this._launchBrowser();
    await this._createBrowserContext();
    await this._enableOfficialRecorder();
    await this._createPage();
    await this._setupContextCommunication();
    await this._setupNetworkMonitoring();
    
    this.recorder.isRecording = true;
    console.log(SUCCESS_MESSAGES.PLAYWRIGHT_INITIALIZED);
  }

  /**
   * 启动浏览器
   * @private
   */
  async _launchBrowser() {
    const args = [
      `--app-name=${this.recorder.options.windowTitle}`,
      ...APP_CONFIG.BROWSER_ARGS
    ];

    this.recorder.browser = await chromium.launch({
      headless: false,
      args
    });
  }

  /**
   * 创建浏览器上下文
   * @private
   */
  async _createBrowserContext() {
    this.recorder.context = await this.recorder.browser.newContext({
      viewport: null,
      /* recordVideo: {
        dir: path.join(__dirname, '../../recordings/'),
        size: { width: 1280, height: 720 }
      } */
    });
  }

  /**
   * 启用官方录制器
   * @private
   */
  async _enableOfficialRecorder() {
    await this.recorder.context._enableRecorder({
      language: this.recorder.options.language,
      mode: 'recording',
      outputFile: this.recorder.options.outputFile,
      handleSIGINT: false,
      launchOptions: { headless: false },
      contextOptions: {}
    });
  }

  /**
   * 创建页面
   * @private
   */
  async _createPage() {
    this.recorder.page = await this.recorder.context.newPage();
  }

  /**
   * 设置 context 级别的通信 - 适用于所有页面
   * @private
   */
  async _setupContextCommunication() {
    try {
      // 确保 messageHandler 存在
      if (!this.recorder.messageHandler) {
        console.warn('⚠️ MessageHandler 未初始化，跳过通信设置');
        return;
      }

      // 创建 context 级别的通信管理器
      this.contextCommunicationManager = new ContextCommunicationManager(
        this.recorder.context, 
        this.recorder.messageHandler.handlePageMessage.bind(this.recorder.messageHandler)
      );
      
      await this.contextCommunicationManager.initialize();
      
      console.log('📡 Context 级别通信已设置 - 适用于所有页面');
    } catch (error) {
      console.error('❌ Context 通信设置失败:', error);
    }
  }

  /**
   * 设置网络监听 - 使用 Context 级别监听，自动覆盖所有页面
   * @private
   */
  async _setupNetworkMonitoring() {
    // 使用 Context 级别的网络监听，自动覆盖所有页面（包括新标签页）
    await this.networkMonitor.setupContextNetworkMonitoring(this.recorder.context);
  }

  /**
   * 清理浏览器资源
   */
  async cleanup() {
    // 清理 context 通信管理器
    if (this.contextCommunicationManager) {
      try {
        await this.contextCommunicationManager.destroy();
      } catch (error) {
        console.warn('⚠️ 清理 context 通信管理器失败:', error);
      }
    }

    // 清理网络监听
    if (this.networkMonitor) {
      this.networkMonitor.cleanup();
    }
    
    if (this.recorder.browser) {
      await this.recorder.browser.close();
      this.recorder.browser = null;
      this.recorder.context = null;
      this.recorder.page = null;
    }
  }

  /**
   * 获取所有活动页面
   */
  getAllPages() {
    if (this.recorder.context) {
      return this.recorder.context.pages();
    }
    return [];
  }
}

module.exports = PlaywrightManager;
