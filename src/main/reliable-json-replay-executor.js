/**
 * 可靠的 JSON 回放执行器 - 使用全局 Playwright 回放 API
 *
 * 这个执行器使用通过 patch 暴露的全局 Playwright 官方回放 API，
 * 确保与官方行为 100% 一致，同时支持多页面管理
 */

const { chromium, firefox, webkit } = require('playwright');
const fs = require('fs');
const path = require('path');

// 加载 playwright-core 以触发全局 API patch
require('playwright-core');

class ReliableJsonReplayExecutor {
  constructor() {
    this.browser = null;
    this.context = null;
    this.pages = new Map(); // pageAlias -> Page
    this.pageAliases = new Map(); // Page -> pageAlias
    this.currentPageAlias = 'page'; // 当前活动页面别名
    this.pageCounter = 0; // 页面计数器，用于生成唯一别名

    // 获取全局 Playwright 回放 API
    this.api = global.playwrightReplayAPI;
    if (!this.api) {
      console.warn('⚠️ 全局 Playwright 回放 API 不可用，将使用备用方案');
    } else {
      console.log('✅ 全局 Playwright 回放 API 已加载');
      console.log('📊 API 版本:', this.api.version);
      console.log('📊 补丁版本:', this.api.patchVersion);
    }
  }

  /**
   * 初始化执行器
   * @param {Object} config - 配置对象
   */
  async initialize(config) {
    try {
      console.log('🚀 初始化可靠的回放执行器...');

      // 启动浏览器
      const browserName = config.browserName || 'chromium';
      const browserEngine = { chromium, firefox, webkit }[browserName];
      
      if (!browserEngine) {
        throw new Error(`不支持的浏览器: ${browserName}`);
      }

      this.browser = await browserEngine.launch({
        headless: false,
        ...config.launchOptions
      });

      this.context = await this.browser.newContext({
        viewport: { width: 1280, height: 720 },
        ...config.contextOptions
      });

      // 设置多页面监听
      this._setupPageListeners();

      // 创建默认页面
      const page = await this.context.newPage();
      await this._registerPage(page, 'page');

      console.log('✅ 浏览器初始化完成');
      
    } catch (error) {
      console.error('❌ 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 设置页面监听器
   * @private
   */
  _setupPageListeners() {
    // 监听新页面创建
    this.context.on('page', async (page) => {
      const pageAlias = this._generatePageAlias();
      await this._registerPage(page, pageAlias);
      console.log(`📄 新页面创建: ${pageAlias} -> ${page.url()}`);
    });

    // 监听页面关闭
    this.context.on('close', () => {
      console.log('🔒 浏览器上下文关闭');
    });
  }

  /**
   * 注册页面
   * @private
   */
  async _registerPage(page, pageAlias) {
    this.pages.set(pageAlias, page);
    this.pageAliases.set(page, pageAlias);
    
    // 设置页面监听器
    page.on('close', () => {
      console.log(`📄 页面关闭: ${pageAlias}`);
      this._unregisterPage(pageAlias);
    });

    // 如果是第一个页面或者当前页面已关闭，设置为当前页面
    if (!this.getCurrentPage() || pageAlias === 'page') {
      this.currentPageAlias = pageAlias;
    }
  }

  /**
   * 注销页面
   * @private
   */
  _unregisterPage(pageAlias) {
    const page = this.pages.get(pageAlias);
    if (page) {
      this.pages.delete(pageAlias);
      this.pageAliases.delete(page);
      
      // 如果关闭的是当前页面，切换到其他页面
      if (this.currentPageAlias === pageAlias) {
        const remainingAliases = Array.from(this.pages.keys());
        this.currentPageAlias = remainingAliases.length > 0 ? remainingAliases[0] : null;
        console.log(`🔄 切换到页面: ${this.currentPageAlias}`);
      }
    }
  }

  /**
   * 生成页面别名
   * @private
   */
  _generatePageAlias() {
    return `page${++this.pageCounter}`;
  }

  /**
   * 获取页面别名
   * @param {string} pageAlias - 页面别名
   * @returns {Page|null} 页面对象
   */
  getPageByAlias(pageAlias) {
    return this.pages.get(pageAlias) || null;
  }

  /**
   * 获取所有页面信息
   * @returns {Array} 页面信息数组
   */
  getAllPagesInfo() {
    return Array.from(this.pages.entries()).map(([alias, page]) => ({
      alias,
      url: page.url(),
      title: page.title ? page.title() : 'Unknown',
      isClosed: page.isClosed()
    }));
  }

  /**
   * 切换到指定页面
   * @param {string} pageAlias - 页面别名
   * @returns {boolean} 是否切换成功
   */
  switchToPage(pageAlias) {
    if (this.pages.has(pageAlias)) {
      this.currentPageAlias = pageAlias;
      console.log(`🔄 切换到页面: ${pageAlias}`);
      return true;
    }
    console.warn(`⚠️ 页面不存在: ${pageAlias}`);
    return false;
  }

  /**
   * 解析动作数据中的页面信息
   * @param {Object} actionData - 动作数据
   * @returns {string} 页面别名
   * @private
   */
  _extractPageAlias(actionData) {
    // 优先使用动作数据中的页面别名
    if (actionData.pageAlias) {
      return actionData.pageAlias;
    }

    // 检查是否有页面索引
    if (actionData.pageIndex !== undefined) {
      const pageAliases = Array.from(this.pages.keys());
      return pageAliases[actionData.pageIndex] || this.currentPageAlias;
    }

    // 特殊动作处理
    if (actionData.name === 'openPage' && actionData.url) {
      // 创建新页面的情况
      return this._generatePageAlias();
    }

    // 默认使用当前页面
    return this.currentPageAlias;
  }

  /**
   * 执行单个动作
   * @param {Object} actionData - 动作数据
   */
  async executeAction(actionData) {
    try {
      console.log(`🎬 执行动作: ${actionData.name}${actionData.selector ? ` -> ${actionData.selector}` : ''}`);

      // 处理页面相关的动作
      if (actionData.name === 'openPage') {
        await this._handleOpenPage(actionData);
        return;
      }

      if (actionData.name === 'closePage') {
        await this._handleClosePage(actionData);
        return;
      }

      // 确定目标页面
      const targetPageAlias = this._extractPageAlias(actionData);
      const targetPage = this.getPageByAlias(targetPageAlias);

      if (!targetPage || targetPage.isClosed()) {
        console.warn(`⚠️ 目标页面不存在或已关闭: ${targetPageAlias}`);
        console.log(`📊 当前可用页面: ${Array.from(this.pages.keys()).join(', ')}`);

        // 如果没有可用页面，使用当前页面
        const fallbackPage = this.getCurrentPage();
        if (fallbackPage) {
          console.log(`🔄 回退到当前页面: ${this.currentPageAlias}`);
          await this._executeActionOnPage(fallbackPage, actionData);
        } else {
          throw new Error('没有可用的页面执行动作');
        }
        return;
      }

      // 切换到目标页面
      if (targetPageAlias !== this.currentPageAlias) {
        this.switchToPage(targetPageAlias);
      }

      // 在目标页面执行动作
      await this._executeActionOnPage(targetPage, actionData);

      // 短暂等待，确保动作完成
      await targetPage.waitForTimeout(100);

    } catch (error) {
      console.error(`❌ 执行动作失败 [${actionData.name}]:`, error.message);
      throw error;
    }
  }

  /**
   * 处理打开页面动作
   * @param {Object} actionData - 动作数据
   * @private
   */
  async _handleOpenPage(actionData) {
    console.log(`🌐 打开新页面: ${actionData.url}`);
    
    try {
      const newPage = await this.context.newPage();
      const pageAlias = this._extractPageAlias(actionData);
      
      await this._registerPage(newPage, pageAlias);
      
      if (actionData.url) {
        await newPage.goto(actionData.url, {
          timeout: 30000,
          waitUntil: 'domcontentloaded'
        });
      }
      
      console.log(`✅ 新页面创建完成: ${pageAlias} -> ${newPage.url()}`);
      
    } catch (error) {
      console.error(`❌ 打开页面失败:`, error);
      throw error;
    }
  }

  /**
   * 处理关闭页面动作
   * @param {Object} actionData - 动作数据
   * @private
   */
  async _handleClosePage(actionData) {
    const pageAlias = this._extractPageAlias(actionData);
    const page = this.getPageByAlias(pageAlias);
    
    if (page && !page.isClosed()) {
      console.log(`🔒 关闭页面: ${pageAlias}`);
      await page.close();
    } else {
      console.warn(`⚠️ 页面不存在或已关闭: ${pageAlias}`);
    }
  }

  /**
   * 在指定页面执行动作
   * @param {Page} page - 页面对象
   * @param {Object} actionData - 动作数据
   * @private
   */
  async _executeActionOnPage(page, actionData) {
    // 优先使用全局 Playwright 回放 API
    if (this.api) {
      await this._executeActionWithGlobalAPI(page, actionData);
    } else {
      // 备用方案：使用原有的执行逻辑
      await this._executeActionReliable(actionData, page);
    }
  }

  /**
   * 使用全局 Playwright 回放 API 执行动作
   * @param {Page} page - 页面对象
   * @param {Object} actionData - 动作数据
   * @private
   */
  async _executeActionWithGlobalAPI(page, actionData) {
    try {
      // 创建页面别名映射（官方 API 需要）
      const pageAliases = new Map();
      const pageAlias = this.pageAliases.get(page) || 'page';
      pageAliases.set(page, pageAlias);

      // 使用全局 API 创建标准的 ActionInContext 对象
      const actionInContext = this.api.createActionInContext(
        pageAlias,
        actionData.framePath || [],
        {
          name: actionData.name,
          selector: actionData.selector,
          url: actionData.url,
          text: actionData.text,
          button: actionData.button || 'left',
          modifiers: actionData.modifiers || 0,
          clickCount: actionData.clickCount || 1,
          key: actionData.key,
          options: actionData.options,
          files: actionData.files,
          position: actionData.position,
          checked: actionData.checked,
          value: actionData.value,
          state: actionData.state,
          substring: actionData.substring
        }
      );

      console.log(`🌍 使用全局 API 执行: ${actionData.name}`);

      // 使用官方的 performAction 函数
      await this.api.performAction(pageAliases, actionInContext);

      console.log(`✅ 全局 API 执行成功: ${actionData.name}`);

    } catch (error) {
      console.log(`⚠️ 全局 API 执行失败，使用备用方案: ${error.message}`);
      // 如果全局 API 失败，回退到原有方案
      await this._executeActionReliable(actionData, page);
    }
  }

  /**
   * 构建完整的选择器，包含 frame 路径 - 使用全局 API 或备用方案
   */
  _buildFullSelector(framePath, selector) {
    // 优先使用全局 API
    if (this.api && this.api.buildFullSelector) {
      return this.api.buildFullSelector(framePath, selector);
    }

    // 备用方案：与官方 Playwright 完全一致的实现
    if (!framePath || framePath.length === 0) {
      return selector;
    }
    return [...framePath, selector].join(' >> internal:control=enter-frame >> ');
  }

  /**
   * 构建点击选项 - 使用全局 API 或备用方案
   */
  _toClickOptions(actionData) {
    // 优先使用全局 API
    if (this.api && this.api.toClickOptions) {
      return this.api.toClickOptions(actionData);
    }

    // 备用方案：简化的实现
    const options = {};
    if (actionData.button && actionData.button !== 'left') {
      options.button = actionData.button;
    }
    if (actionData.clickCount && actionData.clickCount > 1) {
      options.clickCount = actionData.clickCount;
    }
    if (actionData.position) {
      options.position = actionData.position;
    }
    return options;
  }

  /**
   * 使用可靠的 Playwright API 执行动作（备用方案）
   * @param {Object} actionData - 动作数据
   * @param {Page} page - 页面对象（可选，默认使用当前页面）
   * @private
   */
  async _executeActionReliable(actionData, page = null) {
    const targetPage = page || this.getCurrentPage();

    if (!targetPage) {
      throw new Error('没有可用的页面执行动作');
    }

    console.log(`🔄 使用备用方案执行: ${actionData.name}`);
    const kActionTimeout = 15000;

    // 处理 iframe 选择器
    let selector = actionData.selector;
    if (actionData.framePath && actionData.framePath.length > 0) {
      selector = this._buildFullSelector(actionData.framePath, actionData.selector);
      console.log(`🖼️ 跨 iframe 选择器: ${selector}`);
    }

    switch (actionData.name) {
      case 'navigate':
        console.log(`🌐 导航到: ${actionData.url}`);
        await targetPage.goto(actionData.url, {
          timeout: kActionTimeout * 2,
          waitUntil: 'domcontentloaded'
        });
        await targetPage.waitForLoadState('networkidle', { timeout: 8000 });
        console.log(`✅ 导航完成: ${targetPage.url()}`);
        break;

      case 'click':
        console.log(`🖱️ 点击: ${selector}`);
        const clickOptions = {
          ...this._toClickOptions(actionData),
          timeout: kActionTimeout,
          strict: true
        };
        await targetPage.waitForSelector(selector, { state: 'visible', timeout: kActionTimeout });
        await targetPage.click(selector, clickOptions);
        break;

      case 'fill':
        console.log(`✏️ 填写: ${selector} = "${actionData.text}"`);
        await targetPage.waitForSelector(selector, { state: 'visible', timeout: kActionTimeout });
        await targetPage.fill(selector, actionData.text || '', { timeout: kActionTimeout });
        break;

      case 'press':
        console.log(`⌨️ 按键: ${selector} -> ${actionData.key}`);
        await targetPage.waitForSelector(selector, { state: 'visible', timeout: kActionTimeout });
        await targetPage.press(selector, actionData.key, { timeout: kActionTimeout });
        break;

      case 'check':
        console.log(`☑️ 勾选: ${selector}`);
        await targetPage.waitForSelector(selector, { state: 'visible', timeout: kActionTimeout });
        await targetPage.check(selector, { timeout: kActionTimeout });
        break;

      case 'uncheck':
        console.log(`☐ 取消勾选: ${selector}`);
        await targetPage.waitForSelector(selector, { state: 'visible', timeout: kActionTimeout });
        await targetPage.uncheck(selector, { timeout: kActionTimeout });
        break;

      default:
        console.log(`⚠️ 备用方案不支持的动作: ${actionData.name}`);
        throw new Error(`不支持的动作类型: ${actionData.name}`);
    }

    console.log(`✅ 备用方案执行成功: ${actionData.name}`);
  }

  /**
   * 执行 JSON 文件
   * @param {string} jsonFile - JSON 文件路径
   */
  async executeJsonFile(jsonFile) {
    const lines = fs.readFileSync(jsonFile, 'utf-8').split('\n').filter(Boolean);
    
    if (lines.length === 0) {
      throw new Error('JSON 文件为空');
    }
    
    // 第一行是配置
    const config = JSON.parse(lines[0]);
    console.log('🚀 初始化浏览器...', config);
    
    await this.initialize(config);
    
    // 执行所有操作
    for (let i = 1; i < lines.length; i++) {
      const actionData = JSON.parse(lines[i]);
      await this.executeAction(actionData);
    }
    
    console.log('🎉 所有操作执行完成！');
  }

  /**
   * 执行 JSON 字符串数据
   * @param {string} jsonString - JSON 字符串数据
   */
  async executeJsonString(jsonString) {
    const lines = jsonString.split('\n').filter(Boolean);
    
    if (lines.length === 0) {
      throw new Error('JSON 数据为空');
    }
    
    // 第一行是配置
    const config = JSON.parse(lines[0]);
    console.log('🚀 初始化浏览器...', config);
    
    await this.initialize(config);
    
    // 执行所有操作
    for (let i = 1; i < lines.length; i++) {
      const actionData = JSON.parse(lines[i]);
      await this.executeAction(actionData);
    }
    
    console.log('🎉 所有操作执行完成！');
  }

  /**
   * 获取当前页面
   */
  getCurrentPage() {
    return this.pages.get(this.currentPageAlias);
  }

  /**
   * 获取页面数量
   */
  getPageCount() {
    return this.pages.size;
  }

  /**
   * 列出所有页面
   */
  listPages() {
    console.log(`📄 当前页面列表 (${this.pages.size}个):`);
    this.pages.forEach((page, alias) => {
      const isCurrent = alias === this.currentPageAlias;
      console.log(`  ${isCurrent ? '👉' : '  '} ${alias}: ${page.url()} ${page.isClosed() ? '(已关闭)' : ''}`);
    });
  }

  /**
   * 关闭执行器
   */
  async close() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.context = null;
      this.pages.clear();
      this.pageAliases.clear();
      this.currentPageAlias = null;
    }
  }
}

module.exports = { ReliableJsonReplayExecutor };
