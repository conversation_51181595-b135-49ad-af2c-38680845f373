/**
 * 可靠的 JSON 回放执行器 - 支持多页面管理
 * 
 * 这个执行器专注于稳定性和兼容性，优先使用经过验证的直接 Playwright API
 * 而不是依赖可能不稳定的官方 performAction 函数
 */

const { chromium, firefox, webkit } = require('playwright');
const fs = require('fs');
const path = require('path');

class ReliableJsonReplayExecutor {
  constructor() {
    this.browser = null;
    this.context = null;
    this.pages = new Map(); // pageAlias -> Page
    this.pageAliases = new Map(); // Page -> pageAlias
    this.currentPageAlias = 'page'; // 当前活动页面别名
    this.pageCounter = 0; // 页面计数器，用于生成唯一别名
  }

  /**
   * 初始化执行器
   * @param {Object} config - 配置对象
   */
  async initialize(config) {
    try {
      console.log('🚀 初始化可靠的回放执行器...');

      // 启动浏览器
      const browserName = config.browserName || 'chromium';
      const browserEngine = { chromium, firefox, webkit }[browserName];
      
      if (!browserEngine) {
        throw new Error(`不支持的浏览器: ${browserName}`);
      }

      this.browser = await browserEngine.launch({
        headless: false,
        ...config.launchOptions
      });

      this.context = await this.browser.newContext({
        viewport: { width: 1280, height: 720 },
        ...config.contextOptions
      });

      // 设置多页面监听
      this._setupPageListeners();

      // 创建默认页面
      const page = await this.context.newPage();
      await this._registerPage(page, 'page');

      console.log('✅ 浏览器初始化完成');
      
    } catch (error) {
      console.error('❌ 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 设置页面监听器
   * @private
   */
  _setupPageListeners() {
    // 监听新页面创建
    this.context.on('page', async (page) => {
      const pageAlias = this._generatePageAlias();
      await this._registerPage(page, pageAlias);
      console.log(`📄 新页面创建: ${pageAlias} -> ${page.url()}`);
    });

    // 监听页面关闭
    this.context.on('close', () => {
      console.log('🔒 浏览器上下文关闭');
    });
  }

  /**
   * 注册页面
   * @private
   */
  async _registerPage(page, pageAlias) {
    this.pages.set(pageAlias, page);
    this.pageAliases.set(page, pageAlias);
    
    // 设置页面监听器
    page.on('close', () => {
      console.log(`📄 页面关闭: ${pageAlias}`);
      this._unregisterPage(pageAlias);
    });

    // 如果是第一个页面或者当前页面已关闭，设置为当前页面
    if (!this.getCurrentPage() || pageAlias === 'page') {
      this.currentPageAlias = pageAlias;
    }
  }

  /**
   * 注销页面
   * @private
   */
  _unregisterPage(pageAlias) {
    const page = this.pages.get(pageAlias);
    if (page) {
      this.pages.delete(pageAlias);
      this.pageAliases.delete(page);
      
      // 如果关闭的是当前页面，切换到其他页面
      if (this.currentPageAlias === pageAlias) {
        const remainingAliases = Array.from(this.pages.keys());
        this.currentPageAlias = remainingAliases.length > 0 ? remainingAliases[0] : null;
        console.log(`🔄 切换到页面: ${this.currentPageAlias}`);
      }
    }
  }

  /**
   * 生成页面别名
   * @private
   */
  _generatePageAlias() {
    return `page${++this.pageCounter}`;
  }

  /**
   * 获取页面别名
   * @param {string} pageAlias - 页面别名
   * @returns {Page|null} 页面对象
   */
  getPageByAlias(pageAlias) {
    return this.pages.get(pageAlias) || null;
  }

  /**
   * 获取所有页面信息
   * @returns {Array} 页面信息数组
   */
  getAllPagesInfo() {
    return Array.from(this.pages.entries()).map(([alias, page]) => ({
      alias,
      url: page.url(),
      title: page.title ? page.title() : 'Unknown',
      isClosed: page.isClosed()
    }));
  }

  /**
   * 切换到指定页面
   * @param {string} pageAlias - 页面别名
   * @returns {boolean} 是否切换成功
   */
  switchToPage(pageAlias) {
    if (this.pages.has(pageAlias)) {
      this.currentPageAlias = pageAlias;
      console.log(`🔄 切换到页面: ${pageAlias}`);
      return true;
    }
    console.warn(`⚠️ 页面不存在: ${pageAlias}`);
    return false;
  }

  /**
   * 解析动作数据中的页面信息
   * @param {Object} actionData - 动作数据
   * @returns {string} 页面别名
   * @private
   */
  _extractPageAlias(actionData) {
    // 优先使用动作数据中的页面别名
    if (actionData.pageAlias) {
      return actionData.pageAlias;
    }

    // 检查是否有页面索引
    if (actionData.pageIndex !== undefined) {
      const pageAliases = Array.from(this.pages.keys());
      return pageAliases[actionData.pageIndex] || this.currentPageAlias;
    }

    // 特殊动作处理
    if (actionData.name === 'openPage' && actionData.url) {
      // 创建新页面的情况
      return this._generatePageAlias();
    }

    // 默认使用当前页面
    return this.currentPageAlias;
  }

  /**
   * 执行单个动作
   * @param {Object} actionData - 动作数据
   */
  async executeAction(actionData) {
    try {
      console.log(`🎬 执行动作: ${actionData.name}${actionData.selector ? ` -> ${actionData.selector}` : ''}`);

      // 处理页面相关的动作
      if (actionData.name === 'openPage') {
        await this._handleOpenPage(actionData);
        return;
      }

      if (actionData.name === 'closePage') {
        await this._handleClosePage(actionData);
        return;
      }

      // 确定目标页面
      const targetPageAlias = this._extractPageAlias(actionData);
      const targetPage = this.getPageByAlias(targetPageAlias);

      if (!targetPage || targetPage.isClosed()) {
        console.warn(`⚠️ 目标页面不存在或已关闭: ${targetPageAlias}`);
        console.log(`📊 当前可用页面: ${Array.from(this.pages.keys()).join(', ')}`);
        
        // 如果没有可用页面，使用当前页面
        const fallbackPage = this.getCurrentPage();
        if (fallbackPage) {
          console.log(`🔄 回退到当前页面: ${this.currentPageAlias}`);
          await this._executeActionOnPage(fallbackPage, actionData);
        } else {
          throw new Error('没有可用的页面执行动作');
        }
        return;
      }

      // 切换到目标页面
      if (targetPageAlias !== this.currentPageAlias) {
        this.switchToPage(targetPageAlias);
      }

      // 在目标页面执行动作
      await this._executeActionOnPage(targetPage, actionData);

      // 短暂等待，确保动作完成
      await targetPage.waitForTimeout(100);

    } catch (error) {
      console.error(`❌ 执行动作失败 [${actionData.name}]:`, error.message);
      throw error;
    }
  }

  /**
   * 处理打开页面动作
   * @param {Object} actionData - 动作数据
   * @private
   */
  async _handleOpenPage(actionData) {
    console.log(`🌐 打开新页面: ${actionData.url}`);
    
    try {
      const newPage = await this.context.newPage();
      const pageAlias = this._extractPageAlias(actionData);
      
      await this._registerPage(newPage, pageAlias);
      
      if (actionData.url) {
        await newPage.goto(actionData.url, {
          timeout: 30000,
          waitUntil: 'domcontentloaded'
        });
      }
      
      console.log(`✅ 新页面创建完成: ${pageAlias} -> ${newPage.url()}`);
      
    } catch (error) {
      console.error(`❌ 打开页面失败:`, error);
      throw error;
    }
  }

  /**
   * 处理关闭页面动作
   * @param {Object} actionData - 动作数据
   * @private
   */
  async _handleClosePage(actionData) {
    const pageAlias = this._extractPageAlias(actionData);
    const page = this.getPageByAlias(pageAlias);
    
    if (page && !page.isClosed()) {
      console.log(`🔒 关闭页面: ${pageAlias}`);
      await page.close();
    } else {
      console.warn(`⚠️ 页面不存在或已关闭: ${pageAlias}`);
    }
  }

  /**
   * 在指定页面执行动作
   * @param {Page} page - 页面对象
   * @param {Object} actionData - 动作数据
   * @private
   */
  async _executeActionOnPage(page, actionData) {
    // 使用原有的执行逻辑，但是传入特定的页面对象
    await this._executeActionReliable(actionData, page);
  }

  /**
   * 构建完整的选择器，包含 frame 路径 - 与官方 Playwright 完全一致
   * 来源：packages/playwright-core/src/server/recorder/recorderUtils.ts:25-27
   */
  _buildFullSelector(framePath, selector) {
    if (!framePath || framePath.length === 0) {
      return selector;
    }

    // 🔧 使用官方 Playwright 的 frame 选择器语法
    return [...framePath, selector].join(' >> internal:control=enter-frame >> ');
  }

  /**
   * 转换键盘修饰符 - 与官方 Playwright 完全一致
   * 来源：packages/playwright-core/src/server/codegen/language.ts:59-70
   */
  _toKeyboardModifiers(modifiers) {
    const result = [];
    if (modifiers & 1) result.push('Alt');
    if (modifiers & 2) result.push('ControlOrMeta');
    if (modifiers & 4) result.push('ControlOrMeta');
    if (modifiers & 8) result.push('Shift');
    return result;
  }

  /**
   * 构建点击选项 - 与官方 Playwright 完全一致
   * 来源：packages/playwright-core/src/server/recorder/recorderRunner.ts:133-145
   */
  _toClickOptions(actionData) {
    const modifiers = this._toKeyboardModifiers(actionData.modifiers || 0);
    const options = {};
    if (actionData.button !== 'left')
      options.button = actionData.button;
    if (modifiers.length)
      options.modifiers = modifiers;
    if (actionData.clickCount > 1)
      options.clickCount = actionData.clickCount;
    if (actionData.position)
      options.position = actionData.position;
    return options;
  }

  /**
   * 使用可靠的 Playwright API 执行动作
   * @param {Object} actionData - 动作数据
   * @param {Page} page - 页面对象（可选，默认使用当前页面）
   * @private
   */
  async _executeActionReliable(actionData, page = null) {
    const targetPage = page || this.getCurrentPage();
    
    if (!targetPage) {
      throw new Error('没有可用的页面执行动作');
    }

    const kActionTimeout = 15000; // 增加超时时间以适应慢加载的页面

    // 🔧 修复：处理 iframe 选择器，与官方 Playwright 一致
    let selector = actionData.selector;
    if (actionData.framePath && actionData.framePath.length > 0) {
      selector = this._buildFullSelector(actionData.framePath, actionData.selector);
      console.log(`🖼️ 跨 iframe 选择器: ${selector}`);
    }

    switch (actionData.name) {
      case 'navigate':
        console.log(`🌐 导航到: ${actionData.url}`);
        try {
          await targetPage.goto(actionData.url, {
            timeout: kActionTimeout * 2,  // 导航使用更长的超时时间
            waitUntil: 'domcontentloaded'
          });
          
          // 等待页面稳定和网络请求完成
          await targetPage.waitForLoadState('networkidle', { timeout: 8000 });
          
          // 额外等待确保动态内容加载完成
          await targetPage.waitForTimeout(1000);
          
          console.log(`✅ 导航完成: ${targetPage.url()}`);
        } catch (error) {
          console.log(`❌ 导航失败: ${error.message}`);
          throw error;
        }
        break;

      case 'click':
        console.log(`🖱️ 点击: ${selector}`);
        try {
          const clickOptions = {
            ...this._toClickOptions(actionData),
            timeout: kActionTimeout,
            strict: true
          };

          // 等待元素可见并可点击
          await targetPage.waitForSelector(selector, { state: 'visible', timeout: kActionTimeout, strict: true });
          
          // 确保元素在视口中
          await targetPage.locator(selector).first().scrollIntoViewIfNeeded();
          
          // 等待短暂时间确保元素稳定
          await targetPage.waitForTimeout(500);
          
          await targetPage.click(selector, clickOptions);
          
          // 点击后等待页面稳定
          await targetPage.waitForLoadState('networkidle', { timeout: 3000 });
        } catch (error) {
          if (error.name === 'TimeoutError') {
            console.log(`🔍 诊断点击目标 ${selector} 的状态...`);
            
            // 检查元素是否存在
            const elementExists = await targetPage.locator(selector).count() > 0;
            console.log(`📊 元素存在: ${elementExists}`);
            
            if (elementExists) {
              // 获取元素的详细状态
              const elementState = await targetPage.locator(selector).first().evaluate((el) => ({
                visible: el.offsetParent !== null,
                clickable: !el.disabled && window.getComputedStyle(el).pointerEvents !== 'none',
                display: window.getComputedStyle(el).display,
                visibility: window.getComputedStyle(el).visibility,
                textContent: el.textContent?.trim(),
                tagName: el.tagName
              }));
              
              console.log(`📊 元素状态:`, elementState);
              
              throw new Error(`点击失败: 元素 ${selector} 存在但不可点击。状态: ${JSON.stringify(elementState)}`);
            } else {
              // 尝试查找相似的链接
              const similarLinks = await targetPage.$$eval('a', links => 
                links.map(link => ({ 
                  href: link.href, 
                  text: link.textContent?.trim(),
                  role: link.getAttribute('role')
                })).filter(link => link.text && link.text.includes('热点'))
              );
              
              console.log(`📊 页面上的相似链接:`, similarLinks);
              
              throw new Error(`点击失败: 元素 ${selector} 不存在。页面上的相似链接: ${JSON.stringify(similarLinks)}`);
            }
          }
          throw error;
        }
        break;

      case 'fill':
        console.log(`✏️ 填写: ${selector} = "${actionData.text}"`);
        await targetPage.waitForSelector(selector, { state: 'visible', timeout: kActionTimeout, strict: true });
        await targetPage.fill(selector, actionData.text || '', { timeout: kActionTimeout, strict: true });
        break;

      case 'press':
        console.log(`⌨️ 按键: ${selector} -> ${actionData.key}`);
        const modifiers = this._toKeyboardModifiers(actionData.modifiers || 0);
        const shortcut = modifiers.length > 0 ? [...modifiers, actionData.key].join('+') : actionData.key;
        await targetPage.waitForSelector(selector, { state: 'visible', timeout: kActionTimeout, strict: true });
        await targetPage.press(selector, shortcut, { timeout: kActionTimeout, strict: true });
        break;

      case 'select':
        console.log(`📋 选择: ${selector} -> ${JSON.stringify(actionData.options)}`);
        // 🔧 转换选项格式以匹配官方 Playwright
        const values = (actionData.options || []).map(value => ({ value }));
        await targetPage.waitForSelector(selector, { state: 'visible', timeout: kActionTimeout, strict: true });
        // 官方实现：await mainFrame.selectOption(callMetadata, selector, [], values, { timeout: kActionTimeout, strict: true });
        await targetPage.selectOption(selector, values, { timeout: kActionTimeout, strict: true });
        break;

      case 'check':
        console.log(`☑️ 勾选: ${selector}`);
        await targetPage.waitForSelector(selector, { state: 'visible', timeout: kActionTimeout, strict: true });
        await targetPage.check(selector, { timeout: kActionTimeout, strict: true });
        break;

      case 'uncheck':
        console.log(`☐ 取消勾选: ${selector}`);
        await targetPage.waitForSelector(selector, { state: 'visible', timeout: kActionTimeout, strict: true });
        await targetPage.uncheck(selector, { timeout: kActionTimeout, strict: true });
        break;

      case 'hover':
        console.log(`🖱️ 悬停: ${selector}`);
        await targetPage.waitForSelector(selector, { state: 'visible', timeout: kActionTimeout, strict: true });
        await targetPage.hover(selector, { timeout: kActionTimeout, strict: true });
        break;

      case 'setInputFiles':
        console.log(`📁 设置文件: ${selector} -> ${JSON.stringify(actionData.files)}`);
        await targetPage.waitForSelector(selector, { state: 'visible', timeout: kActionTimeout, strict: true });
        await targetPage.setInputFiles(selector, actionData.files || [], { timeout: kActionTimeout, strict: true });
        break;

      case 'dblclick':
        console.log(`🖱️ 双击: ${selector}`);
        const dblclickOptions = {
          ...this._toClickOptions(actionData),
          timeout: kActionTimeout,
          strict: true
        };
        await targetPage.waitForSelector(selector, { state: 'visible', timeout: kActionTimeout, strict: true });
        await targetPage.dblclick(selector, dblclickOptions);
        break;

      case 'type':
        console.log(`⌨️ 输入: ${actionData.selector} -> "${actionData.text}"`);
        await targetPage.waitForSelector(actionData.selector, { state: 'visible', timeout: kActionTimeout });
        await targetPage.type(actionData.selector, actionData.text || '', { timeout: kActionTimeout });
        break;

      case 'clear':
        console.log(`🧹 清空: ${actionData.selector}`);
        await targetPage.waitForSelector(actionData.selector, { state: 'visible', timeout: kActionTimeout });
        await targetPage.fill(actionData.selector, '', { timeout: kActionTimeout });
        break;

      case 'scrollIntoView':
        console.log(`📜 滚动到视图: ${actionData.selector}`);
        await targetPage.waitForSelector(actionData.selector, { timeout: kActionTimeout });
        await targetPage.locator(actionData.selector).scrollIntoViewIfNeeded({ timeout: kActionTimeout });
        break;

      case 'waitForSelector':
        console.log(`⏳ 等待元素: ${actionData.selector}`);
        await targetPage.waitForSelector(actionData.selector, {
          state: actionData.state || 'visible',
          timeout: kActionTimeout,
          strict: true
        });
        break;

      // 🔧 添加断言功能 - 与官方 Playwright 一致
      case 'assertChecked':
        console.log(`✅ 断言选中状态: ${selector} -> ${actionData.checked}`);
        await targetPage.waitForSelector(selector, { state: 'visible', timeout: kActionTimeout, strict: true });
        const isChecked = await targetPage.isChecked(selector, { timeout: kActionTimeout, strict: true });
        if (isChecked !== (actionData.checked !== false)) {
          throw new Error(`断言失败: 元素 ${selector} 期望${actionData.checked !== false ? '选中' : '未选中'}，实际${isChecked ? '选中' : '未选中'}`);
        }
        break;

      case 'assertText':
        console.log(`✅ 断言文本: ${selector} -> ${actionData.text}`);
        try {
          // 首先等待页面稳定，给动态内容更多时间加载
          await targetPage.waitForLoadState('networkidle', { timeout: 5000 });
          
          // 使用更长的超时时间，并添加重试机制
          await targetPage.waitForSelector(selector, { state: 'visible', timeout: kActionTimeout * 2, strict: true });
          
          const actualText = await targetPage.textContent(selector, { timeout: kActionTimeout, strict: true });
          const expectedText = actionData.text || '';
          const substring = actionData.substring !== false;

          if (substring) {
            if (!actualText || !actualText.includes(expectedText)) {
              throw new Error(`断言失败: 元素 ${selector} 文本不包含 "${expectedText}"，实际文本: "${actualText}"`);
            }
          } else {
            if (actualText !== expectedText) {
              throw new Error(`断言失败: 元素 ${selector} 文本不匹配，期望: "${expectedText}"，实际: "${actualText}"`);
            }
          }
        } catch (error) {
          if (error.name === 'TimeoutError') {
            // 增强的故障诊断
            console.log(`🔍 诊断元素 ${selector} 的状态...`);
            
            // 检查元素是否存在（不管是否可见）
            const elementExists = await targetPage.locator(selector).count() > 0;
            console.log(`📊 元素存在: ${elementExists}`);
            
            if (elementExists) {
              // 获取元素的详细状态
              const elementState = await targetPage.locator(selector).first().evaluate((el) => ({
                visible: el.offsetParent !== null,
                display: window.getComputedStyle(el).display,
                visibility: window.getComputedStyle(el).visibility,
                opacity: window.getComputedStyle(el).opacity,
                textContent: el.textContent
              }));
              
              console.log(`📊 元素状态:`, elementState);
              
              throw new Error(`断言失败: 元素 ${selector} 存在但不可见或不可访问。状态: ${JSON.stringify(elementState)}`);
            } else {
              // 列出页面上所有相似的元素
              const similarElements = await targetPage.$$eval('[id*="scopeListItem"]', elements => 
                elements.map(el => ({ id: el.id, text: el.textContent.trim() }))
              );
              
              console.log(`📊 页面上的相似元素:`, similarElements);
              
              throw new Error(`断言失败: 元素 ${selector} 不存在。页面上的相似元素: ${JSON.stringify(similarElements)}`);
            }
          }
          throw error;
        }
        break;

      case 'assertValue':
        console.log(`✅ 断言值: ${selector} -> ${actionData.value}`);
        await targetPage.waitForSelector(selector, { state: 'visible', timeout: kActionTimeout, strict: true });
        const actualValue = await targetPage.inputValue(selector, { timeout: kActionTimeout, strict: true });
        const expectedValue = actionData.value || '';
        if (actualValue !== expectedValue) {
          throw new Error(`断言失败: 元素 ${selector} 值不匹配，期望: "${expectedValue}"，实际: "${actualValue}"`);
        }
        break;

      case 'assertVisible':
        console.log(`✅ 断言可见性: ${selector}`);
        try {
          const isVisible = await targetPage.isVisible(selector, { timeout: kActionTimeout, strict: true });
          if (!isVisible) {
            throw new Error(`断言失败: 元素 ${selector} 不可见`);
          }
        } catch (error) {
          if (error.name === 'TimeoutError') {
            const elementExists = await targetPage.locator(selector).count() > 0;
            if (elementExists) {
              throw new Error(`断言失败: 元素 ${selector} 存在但不可见，请检查页面状态`);
            } else {
              throw new Error(`断言失败: 元素 ${selector} 不存在，可能页面结构已改变`);
            }
          }
          throw error;
        }
        break;

      default:
        console.log(`⚠️ 不支持的动作: ${actionData.name}`);
        break;
    }
  }

  /**
   * 执行 JSON 文件
   * @param {string} jsonFile - JSON 文件路径
   */
  async executeJsonFile(jsonFile) {
    const lines = fs.readFileSync(jsonFile, 'utf-8').split('\n').filter(Boolean);
    
    if (lines.length === 0) {
      throw new Error('JSON 文件为空');
    }
    
    // 第一行是配置
    const config = JSON.parse(lines[0]);
    console.log('🚀 初始化浏览器...', config);
    
    await this.initialize(config);
    
    // 执行所有操作
    for (let i = 1; i < lines.length; i++) {
      const actionData = JSON.parse(lines[i]);
      await this.executeAction(actionData);
    }
    
    console.log('🎉 所有操作执行完成！');
  }

  /**
   * 执行 JSON 字符串数据
   * @param {string} jsonString - JSON 字符串数据
   */
  async executeJsonString(jsonString) {
    const lines = jsonString.split('\n').filter(Boolean);
    
    if (lines.length === 0) {
      throw new Error('JSON 数据为空');
    }
    
    // 第一行是配置
    const config = JSON.parse(lines[0]);
    console.log('🚀 初始化浏览器...', config);
    
    await this.initialize(config);
    
    // 执行所有操作
    for (let i = 1; i < lines.length; i++) {
      const actionData = JSON.parse(lines[i]);
      await this.executeAction(actionData);
    }
    
    console.log('🎉 所有操作执行完成！');
  }

  /**
   * 获取当前页面
   */
  getCurrentPage() {
    return this.pages.get(this.currentPageAlias);
  }

  /**
   * 获取页面数量
   */
  getPageCount() {
    return this.pages.size;
  }

  /**
   * 列出所有页面
   */
  listPages() {
    console.log(`📄 当前页面列表 (${this.pages.size}个):`);
    this.pages.forEach((page, alias) => {
      const isCurrent = alias === this.currentPageAlias;
      console.log(`  ${isCurrent ? '👉' : '  '} ${alias}: ${page.url()} ${page.isClosed() ? '(已关闭)' : ''}`);
    });
  }

  /**
   * 关闭执行器
   */
  async close() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.context = null;
      this.pages.clear();
      this.pageAliases.clear();
      this.currentPageAlias = null;
    }
  }
}

module.exports = { ReliableJsonReplayExecutor };
