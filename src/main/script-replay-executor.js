/**
 * 脚本回放执行器 - 使用全局 Playwright 回放 API
 * 支持两种执行模式：
 * 1. 严格执行模式（strict）：按照executionSteps的步骤执行，最后根据outputVariables提取数据
 * 2. 监听模式（monitoring）：不执行executionSteps，监听用户操作，检查checkpoint，最后提取数据
 */

const { chromium, firefox, webkit } = require('playwright');
const { ReliableJsonReplayExecutor } = require('./reliable-json-replay-executor');

class ScriptReplayExecutor extends ReliableJsonReplayExecutor {
  constructor() {
    super(); // 继承多页面管理功能
    this.monitoringActive = false;
    this.checkpointResults = [];
    this.outputResults = [];
    this.jsonExecutor = null;
  }

  /**
   * 执行脚本数据
   * @param {Object} scriptData - 脚本数据
   */
  async executeScript(scriptData) {
    try {
      console.log('🎯 开始执行脚本:', scriptData.name);
      console.log('📋 执行模式:', scriptData.executionMode);
      
      // 重置结果
      this.checkpointResults = [];
      this.outputResults = [];
      
      // 根据执行模式选择不同的执行策略
      if (scriptData.executionMode === 'strict') {
        await this._executeStrictMode(scriptData);
      } else if (scriptData.executionMode === 'monitoring') {
        await this._executeMonitoringMode(scriptData);
      } else {
        throw new Error(`未知的执行模式: ${scriptData.executionMode}`);
      }
      
      // 返回结果
      return {
        success: true,
        checkpointResults: this.checkpointResults,
        outputResults: this.outputResults,
        summary: {
          totalCheckpoints: scriptData.checkpoint?.length || 0,
          passedCheckpoints: this.checkpointResults.filter(r => r.passed).length,
          totalOutputs: scriptData.outputVariables?.length || 0,
          extractedOutputs: this.outputResults.length
        }
      };
      
    } catch (error) {
      console.error('❌ 脚本执行失败:', error);
      return {
        success: false,
        error: error.message,
        checkpointResults: this.checkpointResults,
        outputResults: this.outputResults
      };
    }
  }

  /**
   * 严格执行模式
   * @param {Object} scriptData - 脚本数据
   * @private
   */
  async _executeStrictMode(scriptData) {
    console.log('🎯 严格执行模式 - 开始执行步骤');
    
    // 1. 执行 executionSteps
    if (scriptData.executionSteps && scriptData.executionSteps.length > 0) {
      console.log(`📋 执行步骤数量: ${scriptData.executionSteps.length}`);
      
      for (let i = 0; i < scriptData.executionSteps.length; i++) {
        const step = scriptData.executionSteps[i];
        console.log(`📝 执行第 ${i + 1} 步: ${step.name || step.type}`);
        
        try {
          // 使用继承的 executeAction 方法，支持多页面
          await this.executeAction(step);
          console.log(`✅ 第 ${i + 1} 步执行成功`);
        } catch (error) {
          console.error(`❌ 第 ${i + 1} 步执行失败:`, error.message);
          throw new Error(`执行步骤 ${i + 1} 失败: ${error.message}`);
        }
      }
    }
    
    // 2. 验证检查点
    if (scriptData.checkpoint && scriptData.checkpoint.length > 0) {
      console.log(`🔍 验证检查点数量: ${scriptData.checkpoint.length}`);
      await this._validateCheckpoints(scriptData.checkpoint);
    }
    
    // 3. 提取输出变量
    if (scriptData.outputVariables && scriptData.outputVariables.length > 0) {
      console.log(`📤 提取输出变量数量: ${scriptData.outputVariables.length}`);
      await this._extractOutputVariables(scriptData.outputVariables);
    }
    
    console.log('✅ 严格执行模式完成');
  }

  /**
   * 监听模式
   * @param {Object} scriptData - 脚本数据
   * @private
   */
  async _executeMonitoringMode(scriptData) {
    console.log('👁️ 监听模式 - 开始监听用户操作');
    
    this.monitoringActive = true;
    
    // 不执行 executionSteps，而是监听用户操作
    console.log('📱 请在浏览器中手动操作，系统将监听检查点状态...');
    
    try {
      // 设置检查点监听器
      await this._setupCheckpointMonitoring(scriptData.checkpoint);
      
      // 等待所有检查点通过或超时
      const checkpointsPassed = await this._waitForCheckpoints(scriptData.checkpoint);
      
      if (checkpointsPassed) {
        console.log('✅ 所有检查点已通过，开始提取输出变量');
        
        // 提取输出变量
        if (scriptData.outputVariables && scriptData.outputVariables.length > 0) {
          await this._extractOutputVariables(scriptData.outputVariables);
        }
      } else {
        console.log('⚠️ 部分检查点未通过或超时');
      }
      
    } finally {
      this.monitoringActive = false;
    }
    
    console.log('✅ 监听模式完成');
  }

  /**
   * 验证检查点
   * @param {Array} checkpoints - 检查点数组
   * @private
   */
  async _validateCheckpoints(checkpoints) {
    console.log('🔍 开始验证检查点');
    
    for (const checkpoint of checkpoints) {
      console.log(`🎯 验证检查点: ${checkpoint.name || checkpoint.type}`);
      
      try {
        const result = await this._validateSingleCheckpoint(checkpoint);
        this.checkpointResults.push(result);
        
        if (result.passed) {
          console.log(`✅ 检查点通过: ${checkpoint.name || checkpoint.type}`);
        } else {
          console.log(`❌ 检查点失败: ${checkpoint.name || checkpoint.type} - ${result.error}`);
        }
      } catch (error) {
        console.error(`❌ 检查点验证异常: ${checkpoint.name || checkpoint.type}`, error);
        this.checkpointResults.push({
          name: checkpoint.name || checkpoint.type,
          type: checkpoint.type,
          passed: false,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    }
  }

  /**
   * 验证单个检查点
   * @param {Object} checkpoint - 检查点对象
   * @returns {Object} 验证结果
   * @private
   */
  async _validateSingleCheckpoint(checkpoint) {
    const result = {
      name: checkpoint.name || checkpoint.type,
      type: checkpoint.type,
      passed: false,
      error: null,
      timestamp: new Date().toISOString()
    };
    
    try {
      // 获取当前页面或指定页面
      const page = this._getPageForCheckpoint(checkpoint);
      
      switch (checkpoint.type) {
        case 'visibility':
          const isVisible = await page.isVisible(checkpoint.selector, { timeout: 5000 });
          result.passed = isVisible;
          if (!isVisible) {
            result.error = `元素不可见: ${checkpoint.selector}`;
          }
          break;
          
        case 'text':
          const textContent = await page.textContent(checkpoint.selector, { timeout: 5000 });
          const expectedText = checkpoint.expectedValue || '';
          result.passed = textContent && textContent.includes(expectedText);
          if (!result.passed) {
            result.error = `文本不匹配，期望包含: "${expectedText}"，实际: "${textContent}"`;
          }
          break;
          
        case 'value':
          const actualValue = await page.inputValue(checkpoint.selector, { timeout: 5000 });
          const expectedValue = checkpoint.expectedValue || '';
          result.passed = actualValue === expectedValue;
          if (!result.passed) {
            result.error = `值不匹配，期望: "${expectedValue}"，实际: "${actualValue}"`;
          }
          break;
          
        case 'url':
          const currentUrl = page.url();
          const expectedUrl = checkpoint.expectedValue || '';
          result.passed = currentUrl.includes(expectedUrl);
          if (!result.passed) {
            result.error = `URL不匹配，期望包含: "${expectedUrl}"，实际: "${currentUrl}"`;
          }
          break;
          
        default:
          throw new Error(`不支持的检查点类型: ${checkpoint.type}`);
      }
      
    } catch (error) {
      result.error = error.message;
    }
    
    return result;
  }

  /**
   * 获取检查点对应的页面
   * @param {Object} checkpoint - 检查点对象
   * @returns {Page} 页面对象
   * @private
   */
  _getPageForCheckpoint(checkpoint) {
    // 如果检查点指定了页面别名，使用指定的页面
    if (checkpoint.pageAlias) {
      const page = this.getPageByAlias(checkpoint.pageAlias);
      if (page) {
        return page;
      }
    }
    
    // 否则使用当前页面
    return this.getCurrentPage();
  }

  /**
   * 提取输出变量
   * @param {Array} outputVariables - 输出变量数组
   * @private
   */
  async _extractOutputVariables(outputVariables) {
    console.log('📤 开始提取输出变量');
    
    for (const variable of outputVariables) {
      console.log(`📝 提取变量: ${variable.name}`);
      
      try {
        let extractedValue;
        
        // 根据变量类型提取值
        if (variable.type === 'network') {
          // 网络变量（这里简化处理，实际实现需要网络监听）
          extractedValue = variable.value || null;
        } else if (variable.selector) {
          // 基于选择器的变量
          const page = this._getPageForVariable(variable);
          extractedValue = await this._extractVariableFromPage(page, variable);
        } else {
          // 预定义变量
          extractedValue = variable.value || null;
        }
        
        this.outputResults.push({
          name: variable.name,
          type: variable.type,
          value: extractedValue,
          selector: variable.selector,
          timestamp: new Date().toISOString()
        });
        
        console.log(`✅ 变量提取成功: ${variable.name} = ${extractedValue}`);
        
      } catch (error) {
        console.error(`❌ 变量提取失败: ${variable.name}`, error);
        this.outputResults.push({
          name: variable.name,
          type: variable.type,
          value: null,
          error: error.message,
          selector: variable.selector,
          timestamp: new Date().toISOString()
        });
      }
    }
  }

  /**
   * 获取变量对应的页面
   * @param {Object} variable - 变量对象
   * @returns {Page} 页面对象
   * @private
   */
  _getPageForVariable(variable) {
    // 如果变量指定了页面别名，使用指定的页面
    if (variable.pageAlias) {
      const page = this.getPageByAlias(variable.pageAlias);
      if (page) {
        return page;
      }
    }
    
    // 否则使用当前页面
    return this.getCurrentPage();
  }

  /**
   * 从页面提取变量值
   * @param {Page} page - 页面对象
   * @param {Object} variable - 变量对象
   * @returns {string} 提取的值
   * @private
   */
  async _extractVariableFromPage(page, variable) {
    const selector = variable.selector;
    
    if (!selector) {
      throw new Error(`变量 ${variable.name} 没有指定选择器`);
    }
    
    // 等待元素存在
    await page.waitForSelector(selector, { timeout: 5000 });
    
    // 根据变量类型提取不同的值
    switch (variable.type) {
      case 'text':
        return await page.textContent(selector);
      case 'value':
        return await page.inputValue(selector);
      case 'href':
        return await page.getAttribute(selector, 'href');
      case 'src':
        return await page.getAttribute(selector, 'src');
      case 'html':
        return await page.innerHTML(selector);
      default:
        // 默认提取文本内容
        return await page.textContent(selector);
    }
  }

  /**
   * 设置检查点监听
   * @param {Array} checkpoints - 检查点数组
   * @private
   */
  async _setupCheckpointMonitoring(checkpoints) {
    console.log('🎯 设置检查点监听');
    
    // 这里可以设置页面监听器来实时检查检查点状态
    // 简化实现，实际可以使用 page.on('request') 等事件
    
    for (const checkpoint of checkpoints) {
      this.checkpointResults.push({
        name: checkpoint.name || checkpoint.type,
        type: checkpoint.type,
        passed: false,
        error: '等待检查',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 等待检查点通过
   * @param {Array} checkpoints - 检查点数组
   * @returns {boolean} 是否所有检查点都通过
   * @private
   */
  async _waitForCheckpoints(checkpoints) {
    console.log('⏳ 等待检查点通过...');
    
    const maxWaitTime = 60000; // 60秒超时
    const checkInterval = 2000; // 2秒检查一次
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime && this.monitoringActive) {
      let allPassed = true;
      
      for (let i = 0; i < checkpoints.length; i++) {
        const checkpoint = checkpoints[i];
        
        try {
          const result = await this._validateSingleCheckpoint(checkpoint);
          this.checkpointResults[i] = result;
          
          if (!result.passed) {
            allPassed = false;
          }
        } catch (error) {
          console.error(`检查点监听异常: ${checkpoint.name}`, error);
          allPassed = false;
        }
      }
      
      if (allPassed) {
        console.log('✅ 所有检查点已通过');
        return true;
      }
      
      // 等待下次检查
      await new Promise(resolve => setTimeout(resolve, checkInterval));
    }
    
    console.log('⏰ 检查点等待超时或被中断');
    return false;
  }

  /**
   * 停止监听模式
   */
  stopMonitoring() {
    console.log('🛑 停止监听模式');
    this.monitoringActive = false;
  }
}

module.exports = { ScriptReplayExecutor }; 