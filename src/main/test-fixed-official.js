/**
 * 测试修正版官方执行器
 */

const { FixedOfficialExecutor } = require('./fixed-official-executor');

async function testFixedOfficialExecutor() {
  console.log('🧪 开始测试修正版官方执行器...');
  
  const executor = new FixedOfficialExecutor({
    enableDiagnostics: true
  });
  
  try {
    // 初始化
    await executor.initialize({
      browserName: 'chromium',
      launchOptions: {
        headless: false,
        devtools: true
      },
      contextOptions: {
        viewport: { width: 1280, height: 720 }
      }
    });
    
    console.log('✅ 初始化成功');
    
    // 测试导航
    await executor.executeAction({
      name: 'navigate',
      url: 'https://www.baidu.com'
    });
    
    console.log('✅ 导航成功');
    
    // 等待页面加载
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 测试填写输入框
    await executor.executeAction({
      name: 'fill',
      selector: '#kw',
      text: 'playwright 官方API测试'
    });
    
    console.log('✅ 填写输入框成功');
    
    // 测试点击搜索按钮
    await executor.executeAction({
      name: 'click',
      selector: '#su'
    });
    
    console.log('✅ 点击搜索按钮成功');
    
    // 等待搜索结果
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // 测试更复杂的操作
    console.log('\n🔍 测试更复杂的操作...');
    
    // 导航到另一个页面
    await executor.executeAction({
      name: 'navigate',
      url: 'https://example.com'
    });
    
    console.log('✅ 导航到 example.com 成功');
    
    // 等待页面加载
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 测试点击页面上的元素
    await executor.executeAction({
      name: 'click',
      selector: 'h1'
    });
    
    console.log('✅ 点击 h1 元素成功');
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('🎉 所有测试都通过了！现在您应该能看到真正的页面跳转和交互');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    console.error('错误堆栈:', error.stack);
  } finally {
    await executor.close();
  }
}

if (require.main === module) {
  testFixedOfficialExecutor().catch(console.error);
}

module.exports = { testFixedOfficialExecutor }; 