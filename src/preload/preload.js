const { contextBridge, ipcRenderer } = require('electron');

// 暴露安全的 API 给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 录制器控制
  recorderControl: (action, data) => ipcRenderer.invoke('recorder-control', action, data),
  getRecorderStatus: () => ipcRenderer.invoke('get-recorder-status'),
  exportCode: () => ipcRenderer.invoke('export-code'),
  selectElement: () => ipcRenderer.invoke('select-element'),
  getElementInfo: (selector) => ipcRenderer.invoke('get-element-info', selector),
  navigateToUrl: (url) => ipcRenderer.invoke('navigate-to-url', url),

  // 变量操作
  injectVariable: (data) => ipcRenderer.invoke('inject-variable', data),

  // JSON 回放功能
  replayJsonData: (jsonData) => ipcRenderer.invoke('replay-json-data', jsonData),
  replayJsonFile: (filePath) => ipcRenderer.invoke('replay-json-file', filePath),
  replayScriptData: (scriptData) => ipcRenderer.invoke('replay-script-data', scriptData),
  stopReplay: () => ipcRenderer.invoke('stop-replay'),

  // 事件监听
  onRecorderStatusUpdate: (callback) => {
    ipcRenderer.on('recorder-status-update', (event, data) => callback(data));
  },
  onReplayStatusUpdate: (callback) => {
    ipcRenderer.on('replay-status-update', (event, data) => callback(data));
  },

  onCodeUpdated: (callback) => {
    ipcRenderer.on('code-updated', (event, data) => callback(data));
  },

  onElementSelected: (callback) => {
    ipcRenderer.on('element-selected', (event, data) => callback(data));
  },

  onModeChanged: (callback) => {
    ipcRenderer.on('mode-changed', (event, data) => callback(data));
  },

  onRecorderPageMessage: (callback) => {
    ipcRenderer.on('recorder-page-message', (event, data) => callback(data));
  },

  onActionRecorded: (callback) => {
    ipcRenderer.on('action-recorded', (event, action) => callback(action));
  },

  onPlaywrightCodeGenerated: (callback) => {
    ipcRenderer.on('playwright-code-generated', (event, data) => callback(data));
  },

  onNetworkRequestCaptured: (callback) => {
    ipcRenderer.on('network-request-captured', (event, data) => callback(data));
  },

  onRecordActionIntercepted: (callback) => {
    ipcRenderer.on('record-action-intercepted', (event, data) => callback(data));
  },

  // 清理监听器
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  }
});
