* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8f9fa;
  font-size: 13px;
  line-height: 1.4;
}

.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  min-height: 44px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.header h1 {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.status-compact {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(255,255,255,0.2);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #e74c3c;
}

.status-dot.recording {
  background-color: #27ae60;
  animation: pulse 1s infinite;
}

.status-dot.paused {
  background-color: #f39c12;
}

.status-dot.inspect {
  background-color: #9b59b6;
  animation: pulse 1.5s infinite;
}

.status-text {
  font-weight: 500;
}

.code-length {
  color: rgba(255,255,255,0.8);
  font-size: 10px;
}

.header-right {
  font-size: 11px;
  color: rgba(255,255,255,0.8);
}

.last-update {
  font-weight: 400;
}

/* 头部状态详细信息 */
.status-details {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 16px;
}

.mode-badge-header {
  background: rgba(255,255,255,0.2);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid rgba(255,255,255,0.3);
}

.mode-badge-header.mode-recording {
  background: rgba(40, 167, 69, 0.3);
  border-color: rgba(40, 167, 69, 0.5);
}

.mode-badge-header.mode-inspecting {
  background: rgba(255, 193, 7, 0.3);
  border-color: rgba(255, 193, 7, 0.5);
}

.mode-badge-header.mode-recording-inspecting {
  background: linear-gradient(45deg, rgba(40, 167, 69, 0.3) 50%, rgba(255, 193, 7, 0.3) 50%);
  border-color: rgba(255, 255, 255, 0.5);
}

.language-badge-header {
  background: rgba(255,255,255,0.15);
  color: white;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.actions-count-badge {
  background: rgba(0, 123, 255, 0.3);
  color: white;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
  border: 1px solid rgba(0, 123, 255, 0.5);
}

.current-selector-header {
  font-size: 10px;
  color: rgba(255,255,255,0.8);
  background: rgba(255,255,255,0.1);
  padding: 2px 6px;
  border-radius: 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  margin-top: 2px;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* 主布局 */
.main-layout {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Tab导航栏样式 */
.tab-navigation {
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  padding: 0;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.tab-btn {
  flex: 1;
  border: none;
  background: transparent;
  padding: 12px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #6c757d;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
  border-bottom: 3px solid transparent;
}

.tab-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.tab-btn.active {
  background: white;
  color: #007bff;
  border-bottom-color: #007bff;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
}

.tab-icon {
  font-size: 16px;
}

.tab-label {
  font-weight: 600;
}

.tab-badge {
  background: #007bff;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 600;
  margin-left: 4px;
}

.tab-btn.active .tab-badge {
  background: #0056b3;
}

/* Tab内容区域 */
.tab-content-area {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.tab-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  background: white;
}

.tab-content.code-tab {
  display: flex;
  flex-direction: column;
  padding: 0;
}

/* 控制组样式 */
.control-group {
  background: white;
  border-bottom: 1px solid #f1f3f5;
}

.group-header {
  background: #f8f9fa;
  padding: 8px 12px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 32px;
}

.group-title {
  font-size: 12px;
  font-weight: 600;
  color: #495057;
}

.data-info {
  font-size: 10px;
  color: #6c757d;
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 8px;
}

/* 按钮样式 */
.button-row {
  display: flex;
  padding: 8px;
  gap: 4px;
}

.icon-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
}

.icon-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.icon-btn:active {
  transform: translateY(0);
}

.icon-btn.primary {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.icon-btn.primary:hover {
  background: #0056b3;
}

.icon-btn.success {
  background: #28a745;
  color: white;
  border-color: #28a745;
}

.icon-btn.success:hover {
  background: #1e7e34;
}

.icon-btn.danger {
  background: #dc3545;
  color: white;
  border-color: #dc3545;
}

.icon-btn.danger:hover {
  background: #c82333;
}

.icon-btn.warning {
  background: #ffc107;
  color: #212529;
  border-color: #ffc107;
}

.icon-btn.warning:hover {
  background: #e0a800;
}

.icon-btn.secondary {
  background: #6c757d;
  color: white;
  border-color: #6c757d;
}

.icon-btn.secondary:hover {
  background: #545b62;
}

.icon-btn.active {
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.icon-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* URL输入组 */
.url-input-group {
  display: flex;
  padding: 8px;
  gap: 4px;
}

.compact-input {
  flex: 1;
  padding: 8px 10px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 12px;
  background: white;
  transition: border-color 0.2s;
  height: 32px;
}

.compact-input:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

/* 状态网格 */
.status-grid {
  padding: 8px 12px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.status-item-compact {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.status-item-compact .label {
  font-size: 10px;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.mode-badge-compact,
.status-badge-compact,
.language-badge-compact {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  text-align: center;
  font-weight: 500;
}

.mode-badge-compact {
  background: #e9ecef;
  color: #495057;
}

.mode-badge-compact.mode-recording {
  background: #d4edda;
  color: #155724;
}

.mode-badge-compact.mode-inspecting {
  background: #d1ecf1;
  color: #0c5460;
}

.mode-badge-compact.mode-recording-inspecting {
  background: linear-gradient(45deg, #d4edda 50%, #d1ecf1 50%);
  color: #495057;
}

.status-badge-compact.active {
  background: #d4edda;
  color: #155724;
}

.status-badge-compact.inactive {
  background: #f8d7da;
  color: #721c24;
}

.language-badge-compact {
  background: #fff3cd;
  color: #856404;
}

.current-selector {
  padding: 8px 12px;
  border-top: 1px solid #e9ecef;
  font-size: 11px;
}

.current-selector .label {
  color: #6c757d;
  margin-right: 6px;
  font-weight: 500;
}

.selector-text {
  color: #495057;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f8f9fa;
  padding: 2px 4px;
  border-radius: 3px;
}

/* 选中元素样式 */
.element-info-compact {
  padding: 8px 12px;
}

.element-basic-compact {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 8px;
}

.element-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 20px;
}

.element-label {
  font-size: 10px;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  width: 40px;
  flex-shrink: 0;
}

.element-value {
  font-size: 11px;
  color: #495057;
  flex: 1;
  text-align: right;
  word-break: break-all;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 选择器紧凑样式 */
.selectors-compact {
  border-top: 1px solid #e9ecef;
  padding-top: 8px;
}

.selector-tabs {
  display: flex;
  margin-bottom: 6px;
}

.selector-tab {
  flex: 1;
  padding: 6px 4px;
  border: none;
  background: #f8f9fa;
  color: #6c757d;
  font-size: 9px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 0;
  transition: all 0.2s;
  line-height: 1.1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

.selector-tab.active {
  background: #007bff;
  color: white;
}

.selector-tab.has-data {
  background: #e8f5e8;
  border: 1px solid #c3e6cb;
}

.selector-tab.has-data.active {
  background: #28a745;
  color: white;
}

.selector-tab:disabled {
  background: #e9ecef;
  color: #adb5bd;
  cursor: not-allowed;
}

.selector-tab:first-child {
  border-radius: 4px 0 0 4px;
}

.selector-tab:last-child {
  border-radius: 0 4px 4px 0;
}

.selector-content {
  background: #f8f9fa;
  padding: 6px;
  border-radius: 4px;
}

.selector-display {
  display: flex;
  align-items: center;
  gap: 6px;
}

.selector-text {
  flex: 1;
  font-size: 10px;
  color: #495057;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  word-break: break-all;
}

.copy-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: #007bff;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}

.copy-btn:hover {
  background: #0056b3;
}

.copy-btn:disabled {
  background: #6c757d;
  opacity: 0.5;
  cursor: not-allowed;
}

/* 回放Tab专用样式 */
.replay-main-controls {
  padding: 16px;
  display: flex;
  justify-content: center;
  gap: 12px;
}

.replay-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  min-width: 160px;
  justify-content: center;
}

.replay-btn.success {
  background: #28a745;
  color: white;
}

.replay-btn.success:hover {
  background: #218838;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.replay-btn.danger {
  background: #dc3545;
  color: white;
}

.replay-btn.danger:hover {
  background: #c82333;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.replay-btn.primary {
  background: #007bff;
  color: white;
}

.replay-btn.primary:hover {
  background: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.replay-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.btn-icon {
  font-size: 16px;
}

.btn-text {
  font-size: 14px;
}

.replay-mode-info {
  padding: 8px 16px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  display: flex;
  gap: 16px;
}

.mode-info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.mode-icon {
  font-size: 14px;
}

.mode-name {
  font-size: 12px;
  font-weight: 600;
  color: #495057;
}

.mode-desc {
  font-size: 11px;
  color: #6c757d;
}

.replay-status-full {
  padding: 12px 16px;
  border-top: 1px solid #e9ecef;
}

.status-message {
  text-align: center;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
}

.status-message.active {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.status-message.completed {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.replay-progress {
  margin-top: 8px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #007bff;
  border-radius: 2px;
  animation: progress-animation 2s ease-in-out infinite;
}

@keyframes progress-animation {
  0% { width: 0%; }
  50% { width: 70%; }
  100% { width: 100%; }
}

.replay-data-info {
  padding: 12px 16px;
  border-top: 1px solid #e9ecef;
}

.data-preview {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  overflow: hidden;
}

.preview-header {
  background: #e9ecef;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 600;
  color: #495057;
}

.preview-content {
  padding: 8px 12px;
  max-height: 150px;
  overflow-y: auto;
}

.json-preview {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  line-height: 1.4;
  margin: 0;
  color: #495057;
}

.json-line {
  padding: 1px 0;
  border-left: 2px solid #dee2e6;
  padding-left: 8px;
  margin-bottom: 2px;
}

.json-more {
  color: #6c757d;
  font-style: italic;
  text-align: center;
  padding: 4px 0;
}

/* 动作列表完整样式 */
.actions-list-full {
  max-height: 400px;
  overflow-y: auto;
  padding: 8px 16px;
}

.action-item-full {
  display: flex;
  gap: 12px;
  padding: 12px;
  margin-bottom: 8px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.action-item-full:hover {
  background: #e9ecef;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.action-index {
  width: 24px;
  height: 24px;
  background: #007bff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 600;
  flex-shrink: 0;
}

.action-content {
  flex: 1;
}

.action-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.action-type-full {
  background: #28a745;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.action-time-full {
  font-size: 11px;
  color: #6c757d;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.action-details-full {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.action-detail-row {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 12px;
}

.detail-label {
  color: #6c757d;
  font-weight: 500;
  min-width: 50px;
  flex-shrink: 0;
}

.detail-value {
  color: #495057;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  word-break: break-all;
  flex: 1;
}

/* 代码Tab专用样式 */
.code-header-full {
  background: #f8f9fa;
  padding: 12px 16px;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 48px;
}

.code-title-full {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.code-controls-full {
  display: flex;
  align-items: center;
  gap: 12px;
}

.format-tabs {
  display: flex;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  overflow: hidden;
}

.format-tab {
  padding: 6px 12px;
  border: none;
  background: white;
  color: #495057;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border-right: 1px solid #dee2e6;
}

.format-tab:last-child {
  border-right: none;
}

.format-tab.active {
  background: #007bff;
  color: white;
}

.format-tab:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.export-btn {
  padding: 6px 12px;
  border: 1px solid #dee2e6;
  background: white;
  color: #495057;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.export-btn:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.code-content-full {
  flex: 1;
  overflow: auto;
  padding: 12px;
  background: #fafbfc;
  height: calc(100vh - 280px);
}

.code-block-full {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #24292e;
  background: white;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 12px;
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  min-height: calc(100vh - 320px);
  max-height: calc(100vh - 320px);
  overflow-y: auto;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.code-info {
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.info-label {
  color: #6c757d;
  font-weight: 500;
}

.info-value {
  color: #495057;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .tab-btn {
    padding: 10px 12px;
    font-size: 12px;
  }
  
  .tab-icon {
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 6px 12px;
  }
  
  .header h1 {
    font-size: 14px;
  }
  
  .status-compact {
    gap: 4px;
    padding: 3px 6px;
  }
  
  .status-details {
    margin-left: 8px;
    gap: 4px;
  }
  
  .mode-badge-header,
  .language-badge-header,
  .actions-count-badge {
    font-size: 9px;
    padding: 1px 4px;
  }
  
  .current-selector-header {
    display: none; /* 在小屏幕上隐藏选择器显示 */
  }
  
  .tab-btn {
    padding: 8px 6px;
    flex-direction: column;
    gap: 4px;
  }
  
  .tab-label {
    font-size: 10px;
  }
  
  .code-content-full {
    padding: 8px;
    height: calc(100vh - 300px);
  }
  
  .code-block-full {
    font-size: 11px;
    padding: 8px;
    min-height: calc(100vh - 340px);
    max-height: calc(100vh - 340px);
  }
  
  .todo-actions {
    flex-direction: column;
  }
  
  .todo-btn {
    width: 100%;
    justify-content: center;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 待办模块样式 */
.todo-list {
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.todo-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.todo-item:hover {
  background: #e9ecef;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.todo-item.completed {
  background: #d4edda;
  border-color: #c3e6cb;
}

.todo-item.completed:hover {
  background: #c3e6cb;
}

.todo-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255,255,255,0.5);
  border-bottom: 1px solid #e9ecef;
}

.todo-item.completed .todo-header {
  background: rgba(212, 237, 218, 0.5);
}

.todo-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.todo-title {
  flex: 1;
  font-size: 13px;
  font-weight: 600;
  color: #495057;
}

.todo-status {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.todo-status.pending {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.todo-status.optional {
  background: #e2e3f1;
  color: #6f42c1;
  border: 1px solid #d1d2e7;
}

.todo-status.completed {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.todo-content {
  padding: 12px;
}

.todo-description {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.4;
  margin: 0 0 12px 0;
}

.todo-actions {
  display: flex;
  gap: 6px;
  margin-top: 8px;
  flex-wrap: wrap; /* 允许按钮换行 */
}

.todo-btn {
  padding: 4px 8px;
  border: 1px solid #dee2e6;
  background: #f8f9fa;
  color: #495057;
  border-radius: 4px;
  cursor: pointer;
  font-size: 10px;
  font-weight: 500;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 3px;
  flex: 0 0 auto; /* 防止按钮被压缩 */
  min-width: fit-content;
}

.todo-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.todo-btn.primary {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.todo-btn.primary:hover {
  background: #0056b3;
  border-color: #0056b3;
}

.todo-btn.secondary {
  background: #6c757d;
  color: white;
  border-color: #6c757d;
}

.todo-btn.secondary:hover {
  background: #545b62;
  border-color: #545b62;
}

.todo-btn.success {
  background: #28a745;
  color: white;
  border-color: #28a745;
}

.todo-btn.success:hover {
  background: #1e7e34;
  border-color: #1c7430;
}

/* 断言检查点相关样式 */
.assertions-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.assertion-item {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 12px;
  transition: all 0.2s ease;
}

.assertion-item:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.assertion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.assertion-type {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  color: white;
  background: #6c757d;
}

.assertion-type.assertion-visibility {
  background: #28a745;
}

.assertion-type.assertion-text {
  background: #17a2b8;
}

.assertion-type.assertion-value {
  background: #6f42c1;
}

.assertion-time {
  font-size: 10px;
  color: #6c757d;
}

.assertion-content {
  margin-top: 8px;
}

.assertion-description {
  font-size: 11px;
  color: #495057;
  margin-bottom: 6px;
}

.assertion-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 预定义数据类型相关样式 */
.predefined-data-section {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
}

.predefined-data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.predefined-title {
  font-size: 12px;
  font-weight: 600;
  color: #495057;
}

.clear-all-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.clear-all-btn:hover {
  background: #c82333;
}

.predefined-data-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.predefined-data-item {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  transition: all 0.2s ease;
}

.predefined-data-item:hover {
  border-color: #adb5bd;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.predefined-data-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.predefined-data-name {
  font-size: 11px;
  font-weight: 600;
  color: #495057;
  min-width: 60px;
}

.predefined-data-value {
  font-size: 10px;
  color: #6c757d;
  flex: 1;
  font-family: 'Consolas', 'Monaco', monospace;
}

.predefined-data-time {
  font-size: 9px;
  color: #adb5bd;
  margin-left: auto;
}

.predefined-data-actions {
  display: flex;
  gap: 6px;
}

.predefined-extract-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 10px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.predefined-extract-btn:hover {
  background: #0056b3;
}

.predefined-clear-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 10px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.predefined-clear-btn:hover {
  background: #545b62;
}

.predefined-summary {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.predefined-summary-item {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 12px;
  transition: all 0.2s ease;
}

.predefined-summary-item:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.summary-name {
  font-size: 12px;
  font-weight: 600;
  color: #495057;
}

.summary-time {
  font-size: 10px;
  color: #6c757d;
}

.summary-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.summary-value {
  font-size: 11px;
  color: #28a745;
  font-family: 'Consolas', 'Monaco', monospace;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 3px;
  border: 1px solid #dee2e6;
}

.summary-selector {
  font-size: 10px;
  color: #6c757d;
  font-family: 'Consolas', 'Monaco', monospace;
}

/* 提取数据相关样式 */
.extracted-data-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.extracted-item {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 12px;
  transition: all 0.2s ease;
}

.extracted-item:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.extracted-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.extracted-name {
  font-size: 12px;
  font-weight: 600;
  color: #495057;
  flex: 1;
  margin-right: 12px;
}

.extracted-time {
  font-size: 10px;
  color: #6c757d;
}

.extracted-content {
  margin-top: 8px;
}

.extracted-description {
  font-size: 11px;
  color: #495057;
  margin-bottom: 6px;
}

.extracted-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 通用详情行样式 */
.detail-row {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 11px;
}

.detail-label {
  color: #6c757d;
  font-weight: 500;
  min-width: 50px;
  flex-shrink: 0;
}

.detail-value {
  color: #495057;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  word-break: break-all;
  flex: 1;
  background: rgba(0,0,0,0.03);
  padding: 1px 4px;
  border-radius: 3px;
}

/* 删除按钮样式 */
.remove-btn {
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 10px;
  transition: all 0.2s ease;
}

.remove-btn:hover {
  background: #f8d7da;
  color: #721c24;
}

/* Network Tab样式 */
.network-tab {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.network-layout {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.network-requests-panel {
  width: 60%;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
}

.network-header {
  background: #f8f9fa;
  padding: 8px 12px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.network-title {
  font-size: 13px;
  font-weight: 600;
  color: #495057;
}

.network-stats {
  display: flex;
  align-items: center;
  gap: 8px;
}

.request-count {
  font-size: 11px;
  color: #6c757d;
}

.clear-btn {
  padding: 4px 8px;
  border: 1px solid #dee2e6;
  background: white;
  color: #6c757d;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s;
}

.clear-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

.requests-table {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.table-header {
  background: #e9ecef;
  padding: 6px 8px;
  display: grid;
  grid-template-columns: 60px 1fr 60px 60px 60px;
  gap: 8px;
  font-size: 11px;
  font-weight: 600;
  color: #495057;
  border-bottom: 1px solid #dee2e6;
}

.table-body {
  flex: 1;
  overflow-y: auto;
}

.request-row {
  display: grid;
  grid-template-columns: 60px 1fr 60px 60px 60px;
  gap: 8px;
  padding: 6px 8px;
  border-bottom: 1px solid #f1f3f4;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 11px;
}

.request-row:hover {
  background: #f8f9fa;
}

.request-row.selected {
  background: #e7f3ff;
  border-left: 3px solid #007bff;
}

.col-method {
  font-weight: 600;
  text-align: center;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 10px;
}

.method-get { background: #d4edda; color: #155724; }
.method-post { background: #fff3cd; color: #856404; }
.method-put { background: #cce5ff; color: #004085; }
.method-delete { background: #f8d7da; color: #721c24; }
.method-patch { background: #e2e3f1; color: #6f42c1; }

.col-url {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: #495057;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.col-status {
  text-align: center;
  font-weight: 500;
}

.status-2xx { color: #28a745; }
.status-3xx { color: #ffc107; }
.status-4xx { color: #dc3545; }
.status-5xx { color: #6f42c1; }

.col-time,
.col-size {
  text-align: right;
  color: #6c757d;
}

/* 请求详情面板 */
.network-details-panel {
  width: 40%;
  background: white;
  display: flex;
  flex-direction: column;
  border-left: 1px solid #e9ecef;
}

.details-header {
  background: #f8f9fa;
  padding: 8px 12px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.details-title {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.method-badge {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 600;
}

.request-url {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  color: #495057;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.close-details {
  background: none;
  border: none;
  font-size: 16px;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-details:hover {
  background: #e9ecef;
  color: #495057;
}

.details-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.detail-section {
  border-bottom: 1px solid #f1f3f4;
}

.section-title {
  background: #f8f9fa;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 600;
  color: #495057;
  border-bottom: 1px solid #e9ecef;
}

.section-content {
  padding: 12px;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.info-item {
  display: flex;
  gap: 8px;
  font-size: 12px;
}

.info-item .label {
  font-weight: 500;
  color: #6c757d;
  min-width: 50px;
}

.info-item .value {
  color: #495057;
}

.json-content {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  line-height: 1.4;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 8px;
  margin: 0;
  overflow-x: auto;
  white-space: pre-wrap;
}

.headers-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.header-item {
  display: flex;
  gap: 8px;
  font-size: 11px;
  padding: 4px 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.header-key {
  font-weight: 500;
  color: #6c757d;
  min-width: 120px;
}

.header-value {
  color: #495057;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  word-break: break-all;
}

/* JSON查看器样式 */
.json-viewer {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  line-height: 1.4;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 8px;
  overflow: auto;
  max-height: 400px;
}

.json-root,
.json-object-content,
.json-array-content {
  margin-left: 16px;
}

.json-root-item,
.json-object-item,
.json-array-item {
  margin: 2px 0;
}

.json-key {
  color: #0066cc;
  font-weight: 500;
  cursor: pointer;
  margin-right: 4px;
}

.json-key:hover {
  background: #e9ecef;
  border-radius: 2px;
}

.json-string { color: #22863a; }
.json-number { color: #6f42c1; }
.json-boolean { color: #e36209; }
.json-null { color: #6c757d; font-style: italic; }
.json-bracket { color: #24292e; font-weight: 600; }
.json-comma { color: #6c757d; }

/* 右键菜单 */
.context-menu {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  overflow: hidden;
  min-width: 150px;
}

.menu-items {
  padding: 4px 0;
}

.menu-item {
  padding: 8px 12px;
  font-size: 12px;
  color: #495057;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.menu-item:hover {
  background: #f8f9fa;
  color: #007bff;
}

/* 网络摘要面板 */
.network-summary {
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 12px 16px;
  max-height: 200px;
  overflow-y: auto;
}

.summary-section {
  margin-bottom: 16px;
}

.summary-section:last-child {
  margin-bottom: 0;
}

.summary-title {
  font-size: 12px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
}

.checkpoints-list,
.variables-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.checkpoint-item,
.variable-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 8px;
  font-size: 11px;
}

.checkpoint-name,
.variable-name {
  font-weight: 600;
  color: #495057;
  display: block;
  margin-bottom: 4px;
}

.checkpoint-url,
.variable-path {
  color: #6c757d;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  display: block;
  margin-bottom: 2px;
}

.variable-value {
  color: #22863a;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .network-requests-panel {
    width: 50%;
  }
  
  .network-details-panel {
    width: 50%;
  }
}

@media (max-width: 768px) {
  .network-layout {
    flex-direction: column;
  }
  
  .network-requests-panel {
    width: 100%;
    height: 40%;
    border-right: none;
    border-bottom: 1px solid #e9ecef;
  }
  .network-requests-panel-full {
    width: 100%;
    height: 100%;
    border-right: none;
    border-bottom: 1px solid #e9ecef;
  }
  
  .network-details-panel {
    width: 100%;
    height: 60%;
    border-left: none;
  }
  
  .table-header,
  .request-row {
    grid-template-columns: 50px 1fr 50px;
  }
  
  .col-time,
  .col-size {
    display: none;
  }
}

/* 选择文本样式 */
::selection {
  background-color: #007bff;
  color: white;
}

::-moz-selection {
  background-color: #007bff;
  color: white;
}

/* 选中元素弹窗样式 */
.element-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.element-modal {
  background: white;
  border-radius: 12px 12px 0 0;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 800px;
  max-height: 60vh;
  animation: slideUp 0.3s ease-out;
  display: flex;
  flex-direction: column;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-header {
  background: #f8f9fa;
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 12px 12px 0 0;
}

.modal-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.modal-close {
  background: none;
  border: none;
  font-size: 16px;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: #e9ecef;
  color: #495057;
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.element-info-modal {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.element-basic-modal {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 8px;
}

.element-modal .element-row {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 0;
  font-size: 12px;
}

.element-modal .element-label {
  color: #6c757d;
  font-weight: 500;
  min-width: 50px;
  flex-shrink: 0;
}

.element-modal .element-value {
  color: #495057;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  word-break: break-all;
  flex: 1;
  background: rgba(0,0,0,0.03);
  padding: 2px 6px;
  border-radius: 3px;
}

.selectors-modal {
  border-top: 1px solid #e9ecef;
  padding-top: 16px;
}

.selector-tabs {
  display: flex;
  margin-bottom: 8px;
  gap: 2px;
}

.selector-tab {
  flex: 1;
  padding: 8px 12px;
  border: none;
  background: #f8f9fa;
  color: #6c757d;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.selector-tab.active {
  background: #007bff;
  color: white;
}

.selector-tab.has-data {
  background: #e8f5e8;
  border: 1px solid #c3e6cb;
}

.selector-tab.has-data.active {
  background: #28a745;
  color: white;
}

.selector-tab:disabled {
  background: #e9ecef;
  color: #adb5bd;
  cursor: not-allowed;
}

.selector-content {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
}

.selector-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.selector-text {
  flex: 1;
  font-size: 11px;
  color: #495057;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  word-break: break-all;
  background: white;
  padding: 6px 8px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.copy-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #007bff;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
  flex-shrink: 0;
}

.copy-btn:hover {
  background: #0056b3;
}

.copy-btn:disabled {
  background: #6c757d;
  opacity: 0.5;
  cursor: not-allowed;
}

.element-save-section {
  border-top: 1px solid #e9ecef;
  padding-top: 16px;
}

.save-input-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.element-name-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 12px;
  background: white;
  transition: border-color 0.2s;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.element-name-input:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.element-name-input:disabled,
.element-name-input[readonly] {
  background: #f8f9fa;
  border-color: #dee2e6;
  color: #6c757d;
  cursor: not-allowed;
}

.element-name-input:disabled:focus,
.element-name-input[readonly]:focus {
  border-color: #dee2e6;
  box-shadow: none;
}

.save-element-btn {
  padding: 8px 16px;
  border: none;
  background: #28a745;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.save-element-btn:hover {
  background: #218838;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .element-modal {
    max-height: 70vh;
    border-radius: 8px 8px 0 0;
  }
  
  .modal-header {
    padding: 10px 12px;
    border-radius: 8px 8px 0 0;
  }
  
  .modal-content {
    padding: 12px;
  }
  
  .element-basic-modal {
    grid-template-columns: 1fr;
  }
  
  .selector-tabs {
    flex-direction: column;
    gap: 4px;
  }
  
  .save-input-group {
    flex-direction: column;
    align-items: stretch;
  }
  
  .save-element-btn {
    justify-content: center;
  }
}

/* 官方断言信息样式 */
.official-assertion-info {
  padding: 12px 16px;
}

.info-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  gap: 12px;
  transition: all 0.2s ease;
}

.info-card:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.info-icon {
  font-size: 24px;
  line-height: 1;
  flex-shrink: 0;
}

.info-content {
  flex: 1;
}

.info-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 6px;
}

.info-description {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 12px;
  line-height: 1.4;
}

.info-features {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 12px;
}

.feature-item {
  font-size: 11px;
  color: #495057;
  background: rgba(0, 123, 255, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  border-left: 3px solid #007bff;
}

.info-tip {
  font-size: 11px;
  color: #28a745;
  background: rgba(40, 167, 69, 0.1);
  padding: 6px 8px;
  border-radius: 4px;
  border-left: 3px solid #28a745;
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-card {
    flex-direction: column;
    gap: 8px;
  }
  
  .info-icon {
    align-self: center;
  }
}

/* 输出Tab样式 */
.output-tab {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.output-header {
  background: #f8f9fa;
  padding: 12px 16px;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 48px;
}

.output-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.output-stats {
  display: flex;
  align-items: center;
  gap: 16px;
}

.output-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6c757d;
}

.output-count {
  background: #007bff;
  color: white;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 600;
  min-width: 18px;
  text-align: center;
}

.output-section {
  background: white;
  border-bottom: 1px solid #f1f3f5;
}

.output-section-header {
  background: #f8f9fa;
  padding: 8px 16px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 36px;
}

.section-title {
  font-size: 13px;
  font-weight: 600;
  color: #495057;
}

.section-count {
  font-size: 11px;
  color: #6c757d;
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 8px;
}

.output-section .assertions-list,
.output-section .extracted-data-list,
.output-section .predefined-summary {
  padding: 12px 16px;
}

.network-variables-list {
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.network-variable-item {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 12px;
  transition: all 0.2s ease;
}

.network-variable-item:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.variable-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.variable-name {
  font-size: 12px;
  font-weight: 600;
  color: #495057;
}

.variable-type {
  font-size: 10px;
  color: #6c757d;
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 4px;
}

.variable-content {
  margin-top: 8px;
}

.variable-description {
  font-size: 11px;
  color: #495057;
  margin-bottom: 6px;
}

.variable-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.output-empty {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  color: #6c757d;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #495057;
}

.empty-description {
  font-size: 13px;
  margin-bottom: 16px;
  line-height: 1.4;
}

.empty-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.empty-item {
  font-size: 12px;
  color: #6c757d;
  padding: 4px 8px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .output-stats {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .output-item {
    font-size: 11px;
  }
  
  .output-section-header {
    padding: 6px 12px;
  }
  
  .output-section .assertions-list,
  .output-section .extracted-data-list,
  .output-section .predefined-summary,
  .network-variables-list {
    padding: 8px 12px;
  }
}

/* 断言弹窗样式 */
.assertion-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 断言弹窗中的选择器选择样式 */
.assertion-selectors,
.checkpoint-selectors {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

.assertion-selectors .selector-label,
.checkpoint-selectors .selector-label {
  font-size: 12px;
  font-weight: 500;
  color: #6c757d;
  margin-bottom: 4px;
}

.assertion-selectors .selector-tabs,
.checkpoint-selectors .selector-tabs {
  display: flex;
  gap: 2px;
  margin-bottom: 8px;
}

.assertion-selectors .selector-tab,
.checkpoint-selectors .selector-tab {
  flex: 1;
  padding: 6px 10px;
  border: none;
  background: #f8f9fa;
  color: #6c757d;
  font-size: 10px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3px;
}

.assertion-selectors .selector-tab.active,
.checkpoint-selectors .selector-tab.active {
  background: #007bff;
  color: white;
}

.assertion-selectors .selector-tab.has-data,
.checkpoint-selectors .selector-tab.has-data {
  background: #e8f5e8;
  border: 1px solid #c3e6cb;
}

.assertion-selectors .selector-tab.has-data.active,
.checkpoint-selectors .selector-tab.has-data.active {
  background: #28a745;
  color: white;
}

.assertion-selectors .selector-content,
.checkpoint-selectors .selector-content {
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.assertion-selectors .selector-display,
.checkpoint-selectors .selector-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.assertion-selectors .selector-text,
.checkpoint-selectors .selector-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  color: #495057;
  background: rgba(0,0,0,0.03);
  padding: 2px 6px;
  border-radius: 3px;
  flex: 1;
  word-break: break-all;
}

.assertion-selectors .copy-btn,
.checkpoint-selectors .copy-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 24px;
}

.assertion-selectors .copy-btn:hover,
.checkpoint-selectors .copy-btn:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.assertion-selectors .copy-btn:disabled,
.checkpoint-selectors .copy-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.assertion-modal {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 500px;
  max-height: 70vh;
  animation: fadeIn 0.3s ease-out;
  display: flex;
  flex-direction: column;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.assertion-modal .modal-header {
  background: #f8f9fa;
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 8px 8px 0 0;
}

.assertion-modal .modal-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 8px;
}

.assertion-modal .modal-close {
  background: none;
  border: none;
  font-size: 16px;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.assertion-modal .modal-close:hover {
  background: #e9ecef;
  color: #495057;
}

.assertion-modal .modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.assertion-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.assertion-type {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.assertion-type-icon {
  font-size: 16px;
}

.assertion-type-name {
  font-size: 13px;
  font-weight: 600;
  color: #495057;
}

.assertion-selector,
.assertion-expected,
.assertion-description {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 12px;
}

.selector-label,
.expected-label,
.description-label {
  color: #6c757d;
  font-weight: 500;
  min-width: 60px;
  flex-shrink: 0;
}

.selector-value,
.expected-value,
.description-value {
  color: #495057;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: rgba(0,0,0,0.03);
  padding: 4px 8px;
  border-radius: 4px;
  flex: 1;
  word-break: break-all;
}

.assertion-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
}

.assertion-save-btn,
.assertion-cancel-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.assertion-save-btn {
  background: #28a745;
  color: white;
}

.assertion-save-btn:hover {
  background: #218838;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.assertion-cancel-btn {
  background: #6c757d;
  color: white;
}

.assertion-cancel-btn:hover {
  background: #545b62;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

/* 输出页面空状态样式 */
.output-empty-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
  color: #6c757d;
  text-align: center;
  background: #f8f9fa;
  border-radius: 6px;
  margin: 8px 16px;
}

.output-empty-section .empty-icon {
  font-size: 32px;
  margin-bottom: 12px;
  opacity: 0.5;
}

.output-empty-section .empty-text {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 6px;
}

.output-empty-section .empty-hint {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .assertion-modal {
    max-width: 90vw;
    margin: 20px;
  }
  
  .assertion-modal .modal-content {
    padding: 12px;
  }
  
  .assertion-selector,
  .assertion-expected,
  .assertion-description {
    flex-direction: column;
    gap: 4px;
  }
  
  .selector-label,
  .expected-label,
  .description-label {
    min-width: auto;
  }
  
  .assertion-actions {
    flex-direction: column;
  }
  
  .assertion-save-btn,
  .assertion-cancel-btn {
    justify-content: center;
    width: 100%;
  }
}

/* 断言监听状态提示样式 */
.assertion-listening-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  margin-top: 8px;
  animation: pulse 2s infinite;
}

.listening-icon {
  font-size: 16px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.listening-text {
  flex: 1;
}

.listening-title {
  font-size: 12px;
  font-weight: 600;
  color: #856404;
  margin-bottom: 2px;
}

.listening-hint {
  font-size: 10px;
  color: #6c757d;
  line-height: 1.3;
}

/* 登录检查点弹窗样式 */
.checkpoint-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkpoint-modal {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 500px;
  max-height: 70vh;
  animation: fadeIn 0.3s ease-out;
  display: flex;
  flex-direction: column;
}

.checkpoint-modal .modal-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 8px 8px 0 0;
}

.checkpoint-modal .modal-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkpoint-modal .modal-close {
  background: none;
  border: none;
  font-size: 16px;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkpoint-modal .modal-close:hover {
  background: #e9ecef;
  color: #495057;
}

.checkpoint-modal .modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.checkpoint-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.checkpoint-modal .checkpoint-type {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border-radius: 6px;
  border: 1px solid #ffeaa7;
}

.checkpoint-type-icon {
  font-size: 16px;
}

.checkpoint-type-name {
  font-size: 13px;
  font-weight: 600;
  color: #856404;
}

.checkpoint-selector,
.checkpoint-expected,
.checkpoint-description {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 12px;
}

.checkpoint-modal .checkpoint-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
}

.checkpoint-save-btn,
.checkpoint-cancel-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.checkpoint-save-btn {
  background: #fd7e14;
  color: white;
}

.checkpoint-save-btn:hover {
  background: #e8590c;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(253, 126, 20, 0.3);
}

.checkpoint-cancel-btn {
  background: #6c757d;
  color: white;
}

.checkpoint-cancel-btn:hover {
  background: #545b62;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

/* 登录检查点监听状态提示样式 */
.checkpoint-listening-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  border: 1px solid #bee5eb;
  border-radius: 6px;
  margin-top: 8px;
  animation: pulse 2s infinite;
}

.checkpoint-listening-status .listening-icon {
  font-size: 16px;
  animation: spin 2s linear infinite;
  color: #0c5460;
}

.checkpoint-listening-status .listening-text {
  flex: 1;
}

.checkpoint-listening-status .listening-title {
  font-size: 12px;
  font-weight: 600;
  color: #0c5460;
  margin-bottom: 2px;
}

.checkpoint-listening-status .listening-hint {
  font-size: 10px;
  color: #6c757d;
  line-height: 1.3;
}

/* 登录检查点列表样式 */
.login-checkpoints-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px 16px;
}

.checkpoint-item {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 12px;
  transition: all 0.2s ease;
}

.checkpoint-item:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.checkpoint-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.checkpoint-type-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.output-section .checkpoint-type {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  color: white;
  background: #fd7e14;
}

.output-section .checkpoint-type.checkpoint-visibility {
  background: #28a745;
}

.output-section .checkpoint-type.checkpoint-text {
  background: #17a2b8;
}

.output-section .checkpoint-type.checkpoint-value {
  background: #6f42c1;
}

.checkpoint-name {
  font-size: 12px;
  font-weight: 600;
  color: #495057;
  flex: 1;
}

.checkpoint-time {
  font-size: 10px;
  color: #6c757d;
}

.checkpoint-content {
  margin-top: 8px;
}

.checkpoint-description {
  font-size: 11px;
  color: #495057;
  margin-bottom: 6px;
}

.checkpoint-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .checkpoint-modal {
    max-width: 90vw;
    margin: 20px;
  }
  
  .checkpoint-modal .modal-content {
    padding: 12px;
  }
  
  .checkpoint-selector,
  .checkpoint-expected,
  .checkpoint-description {
    flex-direction: column;
    gap: 4px;
  }
  
  .checkpoint-modal .checkpoint-actions {
    flex-direction: column;
  }
  
  .checkpoint-save-btn,
  .checkpoint-cancel-btn {
    justify-content: center;
    width: 100%;
  }
  
  .checkpoint-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .checkpoint-type-info {
    width: 100%;
  }
  
  .output-section .checkpoint-actions {
    align-self: flex-end;
  }
}

/* 脚本配置面板样式 */
.script-config-panel {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  border-top: 1px solid #e9ecef;
  padding: 8px 16px;
}

.script-config-content {
  display: flex;
  flex-direction: column;
}

.config-row-compact {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 200px;
}

.config-label {
  font-size: 12px;
  font-weight: 500;
  color: #495057;
  white-space: nowrap;
  flex-shrink: 0;
}

.config-input {
  flex: 1;
  padding: 4px 8px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: white;
  color: #495057;
  transition: border-color 0.2s;
  min-width: 120px;
}

.config-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.config-select {
  flex: 1;
  padding: 4px 8px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 12px;
  background: white;
  color: #495057;
  cursor: pointer;
  transition: border-color 0.2s;
  min-width: 120px;
}

.config-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.config-select:hover {
  border-color: #adb5bd;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .script-config-panel {
    padding: 6px 12px;
  }
  
  .config-row-compact {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .config-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    min-width: auto;
  }
  
  .config-input,
  .config-select {
    width: 100%;
    min-width: auto;
  }
}
