/**
 * 测试全局暴露的 Playwright 回放 API
 * 
 * 运行方式：
 * 1. npm run postinstall  (应用patch)
 * 2. node test-global-replay-api.js
 */

const { chromium } = require('playwright');

// 确保加载 playwright-core 以触发 patch
require('playwright-core');

async function testGlobalReplayAPI() {
  console.log('🧪 测试全局 Playwright 回放 API');
  console.log('='.repeat(50));
  
  // 测试1: 检查全局 API 是否存在
  console.log('\n📋 测试1: 检查全局 API 可用性');
  if (!global.playwrightReplayAPI) {
    console.error('❌ 全局 API 不存在！请确保已应用 patch');
    process.exit(1);
  }
  
  const api = global.playwrightReplayAPI;
  console.log('✅ 全局 API 存在');
  console.log('📊 API 信息:', {
    version: api.version,
    isPatchedAPI: api.isPatchedAPI,
    patchVersion: api.patchVersion,
    functions: Object.keys(api).filter(key => typeof api[key] === 'function')
  });
  
  // 测试2: 检查核心函数
  console.log('\n📋 测试2: 检查核心函数');
  const requiredFunctions = [
    'performAction',
    'toClickOptions', 
    'buildFullSelector',
    'mainFrameForAction',
    'frameForAction',
    'createActionInContext',
    'executeActionSequence'
  ];
  
  for (const funcName of requiredFunctions) {
    if (typeof api[funcName] === 'function') {
      console.log(`✅ ${funcName} - 可用`);
    } else {
      console.log(`❌ ${funcName} - 不可用`);
    }
  }
  
  // 测试3: 测试 buildFullSelector 函数
  console.log('\n📋 测试3: 测试 buildFullSelector');
  const framePath = ['iframe[src*="example"]', 'iframe#nested'];
  const selector = 'button#submit';
  const fullSelector = api.buildFullSelector(framePath, selector);
  console.log('🔍 输入:', { framePath, selector });
  console.log('🔍 输出:', fullSelector);
  console.log('✅ buildFullSelector 工作正常');
  
  // 测试4: 测试 createActionInContext 辅助函数
  console.log('\n📋 测试4: 测试 createActionInContext');
  const actionInContext = api.createActionInContext('page', [], {
    name: 'click',
    selector: 'button',
    button: 'left',
    modifiers: 0,
    clickCount: 1
  });
  
  console.log('🏗️ 创建的 ActionInContext:', JSON.stringify(actionInContext, null, 2));
  console.log('✅ createActionInContext 工作正常');
  
  // 测试5: 实际浏览器测试
  console.log('\n📋 测试5: 实际浏览器测试');
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  const pageAliases = new Map();
  pageAliases.set(page, 'page');
  
  try {
    // 测试导航
    console.log('🌐 测试导航...');
    const navigateAction = api.createActionInContext('page', [], {
      name: 'navigate',
      url: 'https://example.com'
    });
    
    await api.performAction(pageAliases, navigateAction);
    console.log('✅ 导航成功');
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    // 测试点击
    console.log('🖱️ 测试点击...');
    const clickAction = api.createActionInContext('page', [], {
      name: 'click',
      selector: 'h1',
      button: 'left',
      modifiers: 0,
      clickCount: 1
    });
    
    await api.performAction(pageAliases, clickAction);
    console.log('✅ 点击成功');
    
    // 测试批量执行
    console.log('📋 测试批量执行...');
    const actionSequence = [
      api.createActionInContext('page', [], {
        name: 'navigate',
        url: 'https://httpbin.org/forms/post'
      }),
      api.createActionInContext('page', [], {
        name: 'fill',
        selector: 'input[name="custname"]',
        text: 'Test User'
      }),
      api.createActionInContext('page', [], {
        name: 'fill',
        selector: 'input[name="custtel"]',
        text: '************'
      })
    ];
    
    const results = await api.executeActionSequence(pageAliases, actionSequence);
    console.log('✅ 批量执行成功，结果数量:', results.length);
    
    // 测试错误处理
    console.log('⚠️ 测试错误处理...');
    try {
      const invalidAction = api.createActionInContext('page', [], {
        name: 'click',
        selector: 'non-existent-element',
        button: 'left',
        modifiers: 0,
        clickCount: 1
      });
      
      await api.performAction(pageAliases, invalidAction);
      console.log('❌ 应该抛出错误但没有');
    } catch (error) {
      console.log('✅ 正确捕获错误:', error.message.substring(0, 50) + '...');
    }
    
  } finally {
    await browser.close();
  }
  
  console.log('\n🎉 所有测试通过！');
  console.log('='.repeat(50));
  console.log('✅ 全局 Playwright 回放 API 工作正常');
  console.log('📝 可以在任何地方使用 global.playwrightReplayAPI 访问官方回放功能');
}

// 性能测试
async function performanceTest() {
  console.log('\n⚡ 性能测试');
  console.log('-'.repeat(30));
  
  const api = global.playwrightReplayAPI;
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext();
  const page = await context.newPage();
  const pageAliases = new Map();
  pageAliases.set(page, 'page');
  
  try {
    // 导航到测试页面
    await page.goto('https://httpbin.org/forms/post');
    
    // 测试单个动作执行时间
    const startTime = Date.now();
    const fillAction = api.createActionInContext('page', [], {
      name: 'fill',
      selector: 'input[name="custname"]',
      text: 'Performance Test'
    });
    
    await api.performAction(pageAliases, fillAction);
    const singleActionTime = Date.now() - startTime;
    console.log(`📊 单个动作执行时间: ${singleActionTime}ms`);
    
    // 测试批量动作执行时间
    const batchStartTime = Date.now();
    const batchActions = Array.from({ length: 5 }, (_, i) => 
      api.createActionInContext('page', [], {
        name: 'fill',
        selector: 'input[name="custname"]',
        text: `Batch Test ${i + 1}`
      })
    );
    
    await api.executeActionSequence(pageAliases, batchActions);
    const batchTime = Date.now() - batchStartTime;
    console.log(`📊 批量动作执行时间: ${batchTime}ms (${batchActions.length} 个动作)`);
    console.log(`📊 平均每个动作: ${(batchTime / batchActions.length).toFixed(2)}ms`);
    
  } finally {
    await browser.close();
  }
}

// 兼容性测试
function compatibilityTest() {
  console.log('\n🔧 兼容性测试');
  console.log('-'.repeat(30));
  
  const api = global.playwrightReplayAPI;
  
  // 测试不同的动作类型
  const actionTypes = [
    { name: 'navigate', url: 'https://example.com' },
    { name: 'click', selector: 'button', button: 'left', modifiers: 0, clickCount: 1 },
    { name: 'fill', selector: 'input', text: 'test' },
    { name: 'press', selector: 'input', key: 'Enter', modifiers: 0 },
    { name: 'check', selector: 'input[type="checkbox"]' },
    { name: 'uncheck', selector: 'input[type="checkbox"]' },
    { name: 'select', selector: 'select', options: ['option1'] }
  ];
  
  for (const actionData of actionTypes) {
    try {
      const actionInContext = api.createActionInContext('page', [], actionData);
      console.log(`✅ ${actionData.name} - ActionInContext 创建成功`);
      
      // 验证必要字段
      if (!actionInContext.frame || !actionInContext.action || !actionInContext.startTime) {
        console.log(`❌ ${actionData.name} - ActionInContext 结构不完整`);
      }
    } catch (error) {
      console.log(`❌ ${actionData.name} - 创建失败: ${error.message}`);
    }
  }
}

// 主测试函数
async function runAllTests() {
  try {
    await testGlobalReplayAPI();
    await performanceTest();
    compatibilityTest();
    
    console.log('\n🏆 所有测试完成！');
    console.log('📋 总结:');
    console.log('  ✅ 全局 API 暴露成功');
    console.log('  ✅ 核心功能正常工作');
    console.log('  ✅ 错误处理正确');
    console.log('  ✅ 性能表现良好');
    console.log('  ✅ 兼容性测试通过');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  runAllTests();
}

module.exports = {
  testGlobalReplayAPI,
  performanceTest,
  compatibilityTest,
  runAllTests
};
