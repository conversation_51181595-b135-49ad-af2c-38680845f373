/**
 * 多页面测试脚本
 * 测试 ReliableJsonReplayExecutor 的多页面管理功能
 */

const { ReliableJsonReplayExecutor } = require('./src/main/reliable-json-replay-executor');

async function testMultiPageFeatures() {
  console.log('🧪 开始多页面功能测试...');
  
  const executor = new ReliableJsonReplayExecutor();
  
  try {
    // 初始化执行器
    await executor.initialize();
    
    console.log('📄 当前页面数量:', executor.getPageCount());
    executor.listPages();
    
    // 测试1: 打开新页面
    console.log('\n🧪 测试1: 打开新页面');
    await executor.executeAction({
      name: 'openPage',
      url: 'https://httpbin.org/delay/2'
    });
    
    console.log('📄 打开新页面后的页面数量:', executor.getPageCount());
    executor.listPages();
    
    // 测试2: 在多个页面间切换
    console.log('\n🧪 测试2: 页面切换');
    console.log('当前页面URL:', executor.getCurrentPage().url());
    
    // 切换到页面1
    executor.switchToPage('page1');
    console.log('切换到page1后的URL:', executor.getCurrentPage().url());
    
    // 切换回原页面
    executor.switchToPage('page');
    console.log('切换回page后的URL:', executor.getCurrentPage().url());
    
    // 测试3: 在不同页面执行操作
    console.log('\n🧪 测试3: 在不同页面执行操作');
    
    // 在主页面导航
    await executor.executeAction({
      name: 'navigate',
      url: 'https://httpbin.org/html'
    });
    
    // 在page1导航
    await executor.executeAction({
      name: 'navigate',
      url: 'https://httpbin.org/json',
      pageAlias: 'page1'
    });
    
    console.log('主页面URL:', executor.getPageByAlias('page').url());
    console.log('page1 URL:', executor.getPageByAlias('page1').url());
    
    // 测试4: 页面信息查看
    console.log('\n🧪 测试4: 页面信息');
    const pagesInfo = executor.getAllPagesInfo();
    console.log('所有页面信息:', pagesInfo);
    
    // 测试5: 关闭页面
    console.log('\n🧪 测试5: 关闭页面');
    await executor.executeAction({
      name: 'closePage',
      pageAlias: 'page1'
    });
    
    console.log('关闭page1后的页面数量:', executor.getPageCount());
    executor.listPages();
    
    console.log('✅ 多页面功能测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await executor.close();
  }
}

// 运行测试
if (require.main === module) {
  testMultiPageFeatures();
}

module.exports = { testMultiPageFeatures }; 