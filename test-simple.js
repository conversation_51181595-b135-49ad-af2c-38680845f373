/**
 * 简单测试全局 Playwright 回放 API
 * 兼容 Node.js 12+
 */

console.log('🧪 测试全局 Playwright 回放 API');
console.log('Node.js 版本:', process.version);

try {
  // 1. 尝试加载 playwright-core 的核心模块（不启动浏览器）
  console.log('📦 加载核心模块...');
  
  // 直接加载 recorderRunner 模块来触发全局 API 注册
  const recorderRunner = require('./node_modules/playwright-core/lib/server/recorder/recorderRunner.js');
  console.log('✅ recorderRunner 模块加载成功');
  
  // 加载 recorderUtils 来触发辅助函数注册
  const recorderUtils = require('./node_modules/playwright-core/lib/server/recorder/recorderUtils.js');
  console.log('✅ recorderUtils 模块加载成功');
  
  // 2. 检查全局 API 是否存在
  console.log('\n🔍 检查全局 API...');
  if (global.playwrightReplayAPI) {
    console.log('✅ 全局 API 存在！');
    
    const api = global.playwrightReplayAPI;
    
    // 显示 API 信息
    console.log('\n📊 API 信息:');
    console.log('  版本:', api.version);
    console.log('  是否为补丁API:', api.isPatchedAPI);
    console.log('  补丁版本:', api.patchVersion);
    
    // 检查函数
    const functions = Object.keys(api).filter(key => typeof api[key] === 'function');
    console.log('  可用函数数量:', functions.length);
    console.log('  函数列表:', functions);
    
    // 3. 测试核心函数（不需要浏览器）
    console.log('\n🔧 测试核心函数...');
    
    // 测试 buildFullSelector
    if (typeof api.buildFullSelector === 'function') {
      try {
        const framePath = ['iframe#main', 'iframe.content'];
        const selector = 'button.submit';
        const fullSelector = api.buildFullSelector(framePath, selector);
        console.log('✅ buildFullSelector 测试通过');
        console.log('  输入 framePath:', framePath);
        console.log('  输入 selector:', selector);
        console.log('  输出 fullSelector:', fullSelector);
        
        // 验证输出格式
        const expectedPattern = /iframe#main >> internal:control=enter-frame >> iframe\.content >> internal:control=enter-frame >> button\.submit/;
        if (expectedPattern.test(fullSelector)) {
          console.log('  ✅ 输出格式正确');
        } else {
          console.log('  ⚠️ 输出格式可能有问题');
        }
      } catch (error) {
        console.log('❌ buildFullSelector 测试失败:', error.message);
      }
    } else {
      console.log('❌ buildFullSelector 函数不存在');
    }
    
    // 测试 createActionInContext
    if (typeof api.createActionInContext === 'function') {
      try {
        const actionInContext = api.createActionInContext('page', [], {
          name: 'click',
          selector: 'button#test',
          button: 'left',
          modifiers: 0,
          clickCount: 1
        });
        
        console.log('✅ createActionInContext 测试通过');
        console.log('  创建的对象结构:');
        console.log('    frame.pageAlias:', actionInContext.frame.pageAlias);
        console.log('    frame.framePath:', actionInContext.frame.framePath);
        console.log('    action.name:', actionInContext.action.name);
        console.log('    action.selector:', actionInContext.action.selector);
        console.log('    startTime 类型:', typeof actionInContext.startTime);
        
        // 验证对象结构
        const isValid = actionInContext.frame && 
                       actionInContext.action && 
                       typeof actionInContext.startTime === 'number' &&
                       actionInContext.frame.pageAlias === 'page' &&
                       Array.isArray(actionInContext.frame.framePath) &&
                       actionInContext.action.name === 'click';
        
        if (isValid) {
          console.log('  ✅ 对象结构验证通过');
        } else {
          console.log('  ⚠️ 对象结构验证失败');
        }
      } catch (error) {
        console.log('❌ createActionInContext 测试失败:', error.message);
      }
    } else {
      console.log('❌ createActionInContext 函数不存在');
    }
    
    // 测试 toClickOptions
    if (typeof api.toClickOptions === 'function') {
      try {
        const clickAction = {
          button: 'right',
          modifiers: 1,
          clickCount: 2,
          position: { x: 100, y: 200 }
        };
        
        const options = api.toClickOptions(clickAction);
        console.log('✅ toClickOptions 测试通过');
        console.log('  输入:', clickAction);
        console.log('  输出:', options);
        
        // 验证输出
        const hasExpectedFields = options.button === 'right' && 
                                 Array.isArray(options.modifiers) &&
                                 options.clickCount === 2 &&
                                 options.position && 
                                 options.position.x === 100;
        
        if (hasExpectedFields) {
          console.log('  ✅ 输出验证通过');
        } else {
          console.log('  ⚠️ 输出验证失败');
        }
      } catch (error) {
        console.log('❌ toClickOptions 测试失败:', error.message);
      }
    } else {
      console.log('❌ toClickOptions 函数不存在');
    }
    
    // 4. 测试不同动作类型的 ActionInContext 创建
    console.log('\n📋 测试不同动作类型...');
    const actionTypes = [
      { name: 'navigate', url: 'https://example.com' },
      { name: 'click', selector: 'button', button: 'left', modifiers: 0, clickCount: 1 },
      { name: 'fill', selector: 'input[name="test"]', text: 'test value' },
      { name: 'press', selector: 'input', key: 'Enter', modifiers: 0 },
      { name: 'check', selector: 'input[type="checkbox"]' },
      { name: 'uncheck', selector: 'input[type="checkbox"]' }
    ];
    
    let successCount = 0;
    for (const actionData of actionTypes) {
      try {
        const actionInContext = api.createActionInContext('page', [], actionData);
        if (actionInContext && actionInContext.action && actionInContext.action.name === actionData.name) {
          console.log(`  ✅ ${actionData.name} - 创建成功`);
          successCount++;
        } else {
          console.log(`  ❌ ${actionData.name} - 创建失败（结构错误）`);
        }
      } catch (error) {
        console.log(`  ❌ ${actionData.name} - 创建失败: ${error.message}`);
      }
    }
    
    console.log(`\n📊 动作类型测试结果: ${successCount}/${actionTypes.length} 成功`);
    
    // 5. 总结
    console.log('\n🎉 测试完成！');
    console.log('✅ 全局 Playwright 回放 API 基础功能正常');
    console.log('📝 可以使用 global.playwrightReplayAPI 访问官方回放功能');
    console.log('\n💡 下一步可以：');
    console.log('1. 在实际项目中集成使用');
    console.log('2. 结合浏览器实例进行完整测试');
    console.log('3. 在 Electron 应用中使用');
    
  } else {
    console.log('❌ 全局 API 不存在！');
    console.log('可能的原因:');
    console.log('1. patch 没有正确应用');
    console.log('2. 模块加载顺序问题');
    console.log('3. 文件路径错误');
  }
  
} catch (error) {
  console.log('❌ 测试过程中出现错误:');
  console.log('错误信息:', error.message);
  console.log('错误堆栈:', error.stack);
  
  console.log('\n🔧 可能的解决方案:');
  console.log('1. 检查 playwright-core 是否正确安装');
  console.log('2. 检查 patch 是否正确应用');
  console.log('3. 检查文件路径是否正确');
}

console.log('\n✨ 测试结束');
