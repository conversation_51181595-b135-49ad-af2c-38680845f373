/**
 * 不使用浏览器的全局 API 测试
 * 专注于测试函数调用和数据结构
 */

// 加载模块
require('./node_modules/playwright-core/lib/server/recorder/recorderRunner.js');
require('./node_modules/playwright-core/lib/server/recorder/recorderUtils.js');

console.log('🧪 测试全局 Playwright 回放 API (无浏览器)');
console.log('='.repeat(50));

const api = global.playwrightReplayAPI;

if (!api) {
  console.error('❌ 全局 API 不可用');
  process.exit(1);
}

console.log('✅ 全局 API 可用');
console.log('📊 可用函数:', Object.keys(api).filter(k => typeof api[k] === 'function'));

// 测试1: 创建各种动作类型
console.log('\n📋 测试1: 创建各种动作类型');

const testActions = [
  {
    name: 'navigate',
    data: { name: 'navigate', url: 'https://example.com' },
    description: '导航动作'
  },
  {
    name: 'click',
    data: { 
      name: 'click', 
      selector: 'button#test', 
      button: 'left', 
      modifiers: 0, 
      clickCount: 1 
    },
    description: '点击动作'
  },
  {
    name: 'fill',
    data: { 
      name: 'fill', 
      selector: 'input[name="username"]', 
      text: 'testuser' 
    },
    description: '填写动作'
  },
  {
    name: 'press',
    data: { 
      name: 'press', 
      selector: 'input', 
      key: 'Enter', 
      modifiers: 0 
    },
    description: '按键动作'
  },
  {
    name: 'check',
    data: { 
      name: 'check', 
      selector: 'input[type="checkbox"]' 
    },
    description: '勾选动作'
  }
];

const createdActions = [];

for (const test of testActions) {
  try {
    const actionInContext = api.createActionInContext('page', [], test.data);
    createdActions.push(actionInContext);
    
    console.log(`✅ ${test.description} - 创建成功`);
    console.log(`   动作名称: ${actionInContext.action.name}`);
    console.log(`   关键属性: ${JSON.stringify(test.data).substring(0, 50)}...`);
    
    // 验证必要字段
    if (!actionInContext.frame || !actionInContext.action || !actionInContext.startTime) {
      console.log(`   ⚠️ 结构不完整`);
    }
    
  } catch (error) {
    console.log(`❌ ${test.description} - 创建失败: ${error.message}`);
  }
}

// 测试2: buildFullSelector 功能
console.log('\n📋 测试2: buildFullSelector 功能');

const selectorTests = [
  {
    framePath: [],
    selector: 'button#submit',
    description: '无 iframe'
  },
  {
    framePath: ['iframe#main'],
    selector: 'input[name="test"]',
    description: '单层 iframe'
  },
  {
    framePath: ['iframe#outer', 'iframe.inner'],
    selector: 'div.content',
    description: '嵌套 iframe'
  },
  {
    framePath: ['iframe[src*="example"]', 'iframe#nested', 'iframe.deep'],
    selector: 'span.text',
    description: '深层嵌套 iframe'
  }
];

for (const test of selectorTests) {
  try {
    const fullSelector = api.buildFullSelector(test.framePath, test.selector);
    console.log(`✅ ${test.description}:`);
    console.log(`   输入: framePath=${JSON.stringify(test.framePath)}, selector="${test.selector}"`);
    console.log(`   输出: "${fullSelector}"`);
    
    // 验证输出格式
    if (test.framePath.length === 0) {
      if (fullSelector === test.selector) {
        console.log(`   ✅ 格式正确`);
      } else {
        console.log(`   ⚠️ 格式可能有问题`);
      }
    } else {
      const expectedParts = test.framePath.length + 1; // framePath + selector
      const actualParts = fullSelector.split(' >> internal:control=enter-frame >> ').length;
      if (actualParts === expectedParts) {
        console.log(`   ✅ 格式正确 (${actualParts} 部分)`);
      } else {
        console.log(`   ⚠️ 格式可能有问题 (期望 ${expectedParts} 部分，实际 ${actualParts} 部分)`);
      }
    }
    
  } catch (error) {
    console.log(`❌ ${test.description} - 失败: ${error.message}`);
  }
}

// 测试3: toClickOptions 功能
console.log('\n📋 测试3: toClickOptions 功能');

const clickTests = [
  {
    input: { button: 'left', modifiers: 0, clickCount: 1 },
    description: '基础左键点击'
  },
  {
    input: { button: 'right', modifiers: 1, clickCount: 1 },
    description: 'Alt + 右键点击'
  },
  {
    input: { button: 'left', modifiers: 2, clickCount: 2 },
    description: 'Ctrl + 双击'
  },
  {
    input: { button: 'middle', modifiers: 4, clickCount: 1, position: { x: 100, y: 200 } },
    description: 'Shift + 中键点击 (带位置)'
  }
];

for (const test of clickTests) {
  try {
    const options = api.toClickOptions(test.input);
    console.log(`✅ ${test.description}:`);
    console.log(`   输入: ${JSON.stringify(test.input)}`);
    console.log(`   输出: ${JSON.stringify(options)}`);
    
    // 基本验证
    if (typeof options === 'object') {
      console.log(`   ✅ 返回对象类型正确`);
    } else {
      console.log(`   ⚠️ 返回类型不是对象`);
    }
    
  } catch (error) {
    console.log(`❌ ${test.description} - 失败: ${error.message}`);
  }
}

// 测试4: executeActionSequence 功能 (模拟，不实际执行)
console.log('\n📋 测试4: executeActionSequence 结构测试');

if (typeof api.executeActionSequence === 'function') {
  console.log('✅ executeActionSequence 函数存在');
  console.log('   函数类型:', typeof api.executeActionSequence);
  console.log('   函数长度:', api.executeActionSequence.length, '个参数');
  
  // 创建一个模拟的 pageAliases
  const mockPageAliases = new Map();
  // 注意：这里不能实际执行，因为没有真实的页面对象
  
  console.log('   ✅ 函数结构正确 (需要浏览器实例才能实际测试)');
} else {
  console.log('❌ executeActionSequence 函数不存在');
}

// 测试5: collapseActions 功能
console.log('\n📋 测试5: collapseActions 功能');

if (typeof api.collapseActions === 'function') {
  // 创建一些重复的动作用于测试合并
  const duplicateActions = [
    api.createActionInContext('page', [], { name: 'fill', selector: 'input', text: 'first' }),
    api.createActionInContext('page', [], { name: 'fill', selector: 'input', text: 'second' }),
    api.createActionInContext('page', [], { name: 'fill', selector: 'input', text: 'final' })
  ];
  
  // 设置时间间隔
  duplicateActions[1].startTime = duplicateActions[0].startTime + 100;
  duplicateActions[2].startTime = duplicateActions[1].startTime + 100;
  
  try {
    const collapsed = api.collapseActions(duplicateActions);
    console.log(`✅ collapseActions 测试:`);
    console.log(`   输入动作数量: ${duplicateActions.length}`);
    console.log(`   输出动作数量: ${collapsed.length}`);
    console.log(`   合并效果: ${duplicateActions.length > collapsed.length ? '成功合并' : '未合并'}`);
    
    if (collapsed.length > 0) {
      console.log(`   最终文本: "${collapsed[collapsed.length - 1].action.text}"`);
    }
    
  } catch (error) {
    console.log(`❌ collapseActions 测试失败: ${error.message}`);
  }
} else {
  console.log('❌ collapseActions 函数不存在');
}

// 总结
console.log('\n🎉 测试完成！');
console.log('📊 总结:');
console.log(`   ✅ 创建了 ${createdActions.length} 个动作对象`);
console.log(`   ✅ 测试了 ${selectorTests.length} 种选择器场景`);
console.log(`   ✅ 测试了 ${clickTests.length} 种点击配置`);
console.log('   ✅ 所有核心函数结构正确');

console.log('\n💡 下一步建议:');
console.log('   1. 在支持 Playwright 的 Node.js 环境中测试浏览器功能');
console.log('   2. 集成到实际项目中使用');
console.log('   3. 测试更复杂的动作序列');

console.log('\n✨ 无浏览器测试结束');
